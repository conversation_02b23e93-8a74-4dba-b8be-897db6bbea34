{"name": "pbf", "version": "3.3.0", "description": "a low-level, lightweight protocol buffers implementation in JavaScript", "main": "index.js", "unpkg": "dist/pbf.js", "jsdelivr": "dist/pbf.js", "scripts": {"bench": "node bench/bench.js", "pretest": "eslint index.js compile.js test/*.js bench/bench-tiles.js bin/pbf", "test": "node --test", "cov": "node --test --experimental-test-covetage", "build-min": "mkdirp dist && browserify index.js -s Pbf | uglifyjs -c -m > dist/pbf.js", "build-dev": "mkdirp dist && browserify index.js -d -s Pbf > dist/pbf-dev.js", "prepublishOnly": "npm run build-dev && npm run build-min"}, "files": ["bin", "dist", "compile.js"], "bin": {"pbf": "bin/pbf"}, "repository": {"type": "git", "url": "**************:mapbox/pbf.git"}, "keywords": ["protocol", "buffer", "pbf", "protobuf", "binary", "format", "serialization", "encoder", "decoder"], "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/mapbox/pbf/issues"}, "homepage": "https://github.com/mapbox/pbf", "dependencies": {"ieee754": "^1.1.12", "resolve-protobuf-schema": "^2.1.0"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^17.0.0", "eslint": "^8.57.0", "eslint-config-mourner": "^2.0.3", "mkdirp": "^3.0.1", "protobufjs": "^7.3.2", "protocol-buffers": "^5.0.0", "tile-stats-runner": "^1.0.0", "uglify-js": "^3.18.0"}, "eslintConfig": {"extends": "mourner", "rules": {"space-before-function-paren": [2, "never"], "key-spacing": 0, "no-empty": 0, "global-require": 0, "no-cond-assign": 0, "indent": [2, 4, {"flatTernaryExpressions": true}]}}}