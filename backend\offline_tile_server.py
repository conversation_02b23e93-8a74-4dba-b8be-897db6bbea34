#!/usr/bin/env python3
"""
Serveur de tuiles offline pour C2-EW
Sert des tuiles raster professionnelles stockées localement
"""

import os
import io
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image, ImageDraw, ImageFont
import uvicorn

app = FastAPI(title="C2-EW Offline Tile Server", version="1.0.0")

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dossier des tuiles
TILES_DIR = Path("tiles_offline")
TILES_DIR.mkdir(exist_ok=True)

def create_professional_tile(z, x, y):
    """Crée une tuile professionnelle style CARTO"""
    
    # Couleurs professionnelles (style CARTO light)
    COLORS = {
        'background': '#f8f9fa',
        'water': '#a8d8ea',
        'land': '#ffffff',
        'border': '#dee2e6',
        'text': '#495057',
        'grid': '#e9ecef'
    }
    
    # Créer une image 256x256
    img = Image.new('RGB', (256, 256), color=COLORS['background'])
    draw = ImageDraw.Draw(img)
    
    # Dessiner le fond (terre)
    draw.rectangle([0, 0, 256, 256], fill=COLORS['land'])
    
    # Ajouter une grille subtile pour les niveaux de zoom élevés
    if z >= 8:
        grid_size = max(16, 256 // (2 ** (z - 8)))
        for i in range(0, 256, grid_size):
            draw.line([(i, 0), (i, 256)], fill=COLORS['grid'], width=1)
            draw.line([(0, i), (256, i)], fill=COLORS['grid'], width=1)
    
    # Simuler des zones d'eau (coins)
    if z >= 6:
        # Coin supérieur gauche
        draw.polygon([(0, 0), (50, 0), (0, 50)], fill=COLORS['water'])
        # Coin inférieur droit
        draw.polygon([(256, 256), (206, 256), (256, 206)], fill=COLORS['water'])
    
    # Simuler des frontières/routes
    if z >= 7:
        # Ligne diagonale (route/frontière)
        draw.line([(0, 128), (256, 128)], fill=COLORS['border'], width=2)
        draw.line([(128, 0), (128, 256)], fill=COLORS['border'], width=2)
    
    # Ajouter des détails géographiques simulés pour le Maroc
    if z >= 8:
        # Simuler des villes (points)
        city_positions = [(64, 64), (128, 128), (192, 192)]
        for pos in city_positions:
            draw.ellipse([pos[0]-3, pos[1]-3, pos[0]+3, pos[1]+3],
                        fill=COLORS['text'], outline=COLORS['background'])

    # Ajouter les coordonnées de la tuile (seulement en mode debug)
    if z <= 5:  # Afficher seulement pour les niveaux de zoom très faibles
        try:
            font = ImageFont.load_default()
            text = f"{z}/{x}/{y}"
            # Fond semi-transparent pour le texte
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            draw.rectangle([5, 5, 5 + text_width + 4, 5 + text_height + 4],
                          fill=(255, 255, 255, 128))
            draw.text((7, 7), text, fill=COLORS['text'], font=font)
        except:
            draw.text((10, 10), f"{z}/{x}/{y}", fill=COLORS['text'])
    
    return img

def get_tile_path(z, x, y):
    """Retourne le chemin vers une tuile"""
    return TILES_DIR / str(z) / str(x) / f"{y}.png"

@app.get("/")
async def root():
    """Page d'accueil du serveur de tuiles"""
    return {
        "service": "C2-EW Offline Tile Server",
        "version": "1.0.0",
        "status": "operational",
        "tile_url": "http://localhost:8002/{z}/{x}/{y}.png",
        "zoom_levels": "5-15",
        "format": "PNG"
    }

@app.get("/tiles/{z}/{x}/{y}.png")
async def get_tile(z: int, x: int, y: int):
    """Servir une tuile raster PNG"""
    
    # Validation des paramètres
    if z < 0 or z > 18 or x < 0 or y < 0:
        raise HTTPException(status_code=400, detail="Paramètres de tuile invalides")
    
    tile_path = get_tile_path(z, x, y)
    
    # Si la tuile existe, la servir
    if tile_path.exists():
        with open(tile_path, 'rb') as f:
            return Response(
                content=f.read(),
                media_type="image/png",
                headers={
                    "Cache-Control": "public, max-age=86400",
                    "Access-Control-Allow-Origin": "*"
                }
            )
    
    # Sinon, créer la tuile à la volée
    try:
        img = create_professional_tile(z, x, y)
        
        # Sauvegarder pour la prochaine fois
        tile_path.parent.mkdir(parents=True, exist_ok=True)
        img.save(tile_path, 'PNG', optimize=True)
        
        # Retourner l'image
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG', optimize=True)
        img_bytes.seek(0)
        
        return Response(
            content=img_bytes.getvalue(),
            media_type="image/png",
            headers={
                "Cache-Control": "public, max-age=86400",
                "Access-Control-Allow-Origin": "*"
            }
        )
        
    except Exception as e:
        print(f"❌ Erreur création tuile {z}/{x}/{y}: {e}")
        raise HTTPException(status_code=500, detail="Erreur de génération de tuile")

@app.get("/status")
async def status():
    """Statut du serveur"""
    total_tiles = 0
    
    # Compter les tuiles existantes
    if TILES_DIR.exists():
        for z_dir in TILES_DIR.iterdir():
            if z_dir.is_dir():
                for x_dir in z_dir.iterdir():
                    if x_dir.is_dir():
                        total_tiles += len([f for f in x_dir.iterdir() if f.suffix == '.png'])
    
    return {
        "status": "online",
        "tiles_cached": total_tiles,
        "tiles_directory": str(TILES_DIR.absolute()),
        "supported_formats": ["PNG"],
        "zoom_range": "0-18"
    }

if __name__ == "__main__":
    print("🗺️ Démarrage du serveur de tuiles offline C2-EW...")
    print(f"📁 Dossier des tuiles: {TILES_DIR.absolute()}")
    print("🌐 Serveur disponible sur: http://localhost:8002")
    print("📋 URL des tuiles: http://localhost:8002/tiles/{z}/{x}/{y}.png")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8002,
        log_level="info"
    )
