import { useMap } from 'react-leaflet';
import { useEffect, useRef } from 'react';
import L from 'leaflet';

const SimpleVectorTileLayer = ({
  url = 'http://localhost:8001/tiles/{z}/{x}/{y}.pbf',
  visible = true
}) => {
  const map = useMap();
  const layerRef = useRef(null);

  useEffect(() => {
    if (!map || !visible) return;

    // Vérifier si L.VectorGrid est disponible
    if (!window.L || !window.L.VectorGrid) {
      console.warn('⚠️ leaflet.vectorgrid non disponible, en attente...');

      // Attendre que le script soit chargé
      const checkVectorGrid = () => {
        if (window.L && window.L.VectorGrid) {
          console.log('✅ leaflet.vectorgrid disponible');
          createVectorLayer();
        } else {
          setTimeout(checkVectorGrid, 100);
        }
      };

      checkVectorGrid();
      return;
    }

    createVectorLayer();

    function createVectorLayer() {
      try {
        // Configuration des styles pour les tuiles vectorielles
        const vectorTileOptions = {
          vectorTileLayerStyles: {
            // Style par défaut pour toutes les features
            '': {
              fill: false,
              stroke: true,
              color: '#1e40af',
              weight: 2,
              opacity: 0.8
            },
            // Style pour les frontières
            boundaries: {
              fill: false,
              stroke: true,
              color: '#1e40af',
              weight: 3,
              opacity: 0.8,
              dashArray: '5, 5'
            },
            // Style pour les villes
            cities: {
              fill: true,
              fillColor: '#3b82f6',
              fillOpacity: 0.8,
              stroke: true,
              color: '#ffffff',
              weight: 2,
              radius: 8
            }
          },
          interactive: true,
          getFeatureId: function(f) {
            return f.properties.id || f.properties.name;
          }
        };

        // Créer la couche de tuiles vectorielles
        const vectorGrid = L.vectorGrid.protobuf(url, vectorTileOptions);
        
        // Ajouter les événements
        vectorGrid.on('click', function(e) {
          console.log('Clic sur tuile vectorielle:', e);
          
          if (e.layer && e.layer.properties) {
            const props = e.layer.properties;
            let popupContent = '<div style="font-family: Inter, sans-serif;">';
            
            if (props.name) {
              popupContent += `<h4 style="color: #1e40af; margin: 0 0 8px 0;">${props.name}</h4>`;
            }
            
            if (props.name_ar) {
              popupContent += `<p style="direction: rtl; margin: 4px 0;">${props.name_ar}</p>`;
            }
            
            if (props.type) {
              popupContent += `<p style="font-size: 12px; margin: 4px 0;">Type: ${props.type}</p>`;
            }
            
            if (props.population) {
              popupContent += `<p style="font-size: 12px; margin: 4px 0;">Population: ${props.population.toLocaleString()}</p>`;
            }
            
            if (props.capital === 'yes') {
              popupContent += `<p style="color: #1e40af; font-weight: bold; font-size: 12px;">🏛️ Capitale</p>`;
            }
            
            popupContent += '</div>';
            
            // Créer et afficher le popup
            L.popup()
              .setLatLng(e.latlng)
              .setContent(popupContent)
              .openOn(map);
          }
        });

        vectorGrid.on('mouseover', function(e) {
          if (e.layer) {
            map.getContainer().style.cursor = 'pointer';
          }
        });

        vectorGrid.on('mouseout', function(e) {
          map.getContainer().style.cursor = '';
        });

        // Ajouter la couche à la carte
        vectorGrid.addTo(map);
        layerRef.current = vectorGrid;

        console.log('✅ Couche de tuiles vectorielles ajoutée:', url);

      } catch (error) {
        console.error('❌ Erreur lors de la création de la couche vectorielle:', error);
      }
    }

    // Nettoyage
    return () => {
      if (layerRef.current && map) {
        try {
          map.removeLayer(layerRef.current);
          layerRef.current = null;
          console.log('🧹 Couche de tuiles vectorielles supprimée');
        } catch (error) {
          console.error('Erreur lors de la suppression de la couche:', error);
        }
      }
    };
  }, [map, url, visible]);

  // Gérer la visibilité
  useEffect(() => {
    if (layerRef.current && map) {
      if (visible) {
        if (!map.hasLayer(layerRef.current)) {
          map.addLayer(layerRef.current);
        }
      } else {
        if (map.hasLayer(layerRef.current)) {
          map.removeLayer(layerRef.current);
        }
      }
    }
  }, [visible, map]);

  return null;
};

export default SimpleVectorTileLayer;
