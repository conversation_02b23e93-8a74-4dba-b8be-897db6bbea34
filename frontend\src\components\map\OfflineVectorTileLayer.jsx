import { useMap } from 'react-leaflet';
import { useEffect, useRef, useState } from 'react';
import L from 'leaflet';

const OfflineVectorTileLayer = ({ 
  url = 'http://localhost:8001/tiles/{z}/{x}/{y}.pbf',
  visible = true,
  onError = null,
  debug = false
}) => {
  const map = useMap();
  const layerRef = useRef(null);
  const [status, setStatus] = useState('loading'); // 'loading', 'ready', 'error'
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  // Fonction pour vérifier la connectivité au serveur
  const checkServerConnection = async () => {
    try {
      const response = await fetch('http://localhost:8001/tiles/metadata', {
        method: 'GET',
        timeout: 5000
      });
      return response.ok;
    } catch (error) {
      console.warn('⚠️ Serveur de tuiles non accessible:', error.message);
      return false;
    }
  };

  // Fonction pour charger leaflet.vectorgrid
  const loadVectorGridScript = () => {
    return new Promise((resolve, reject) => {
      // Vérifier si déjà chargé
      if (window.L && window.L.VectorGrid) {
        resolve();
        return;
      }

      // Créer le script
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/leaflet.vectorgrid/1.3.0/Leaflet.VectorGrid.bundled.min.js';
      script.onload = () => {
        if (window.L && window.L.VectorGrid) {
          console.log('✅ leaflet.vectorgrid chargé avec succès');
          resolve();
        } else {
          reject(new Error('Échec du chargement de leaflet.vectorgrid'));
        }
      };
      script.onerror = () => reject(new Error('Erreur de chargement du script'));
      
      document.head.appendChild(script);
    });
  };

  // Fonction pour créer la couche vectorielle
  const createVectorLayer = async () => {
    try {
      // Vérifier la connexion au serveur
      const serverAvailable = await checkServerConnection();
      if (!serverAvailable) {
        throw new Error('Serveur de tuiles non accessible');
      }

      // Configuration optimisée pour les tuiles du Maroc
      const vectorTileOptions = {
        // Styles pour différents types de features
        vectorTileLayerStyles: {
          // Style par défaut
          '': {
            fill: false,
            stroke: true,
            color: '#1e40af',
            weight: 2,
            opacity: 0.8,
            interactive: true
          },
          // Frontières administratives
          'admin_0': {
            fill: false,
            stroke: true,
            color: '#dc2626',
            weight: 3,
            opacity: 0.9,
            dashArray: '8, 4'
          },
          'admin_1': {
            fill: false,
            stroke: true,
            color: '#1e40af',
            weight: 2,
            opacity: 0.7,
            dashArray: '5, 3'
          },
          'admin_2': {
            fill: false,
            stroke: true,
            color: '#059669',
            weight: 1,
            opacity: 0.6
          },
          // Villes et localités
          'populated_places': {
            fill: true,
            fillColor: '#3b82f6',
            fillOpacity: 0.8,
            stroke: true,
            color: '#ffffff',
            weight: 2,
            radius: 6
          },
          // Routes principales
          'roads': {
            fill: false,
            stroke: true,
            color: '#f59e0b',
            weight: 2,
            opacity: 0.7
          },
          // Cours d'eau
          'water': {
            fill: true,
            fillColor: '#0ea5e9',
            fillOpacity: 0.6,
            stroke: true,
            color: '#0284c7',
            weight: 1
          }
        },
        
        // Configuration des interactions
        interactive: true,
        
        // Fonction pour obtenir l'ID unique de chaque feature
        getFeatureId: function(feature) {
          return feature.properties.id || 
                 feature.properties.name || 
                 feature.properties.NAME || 
                 Math.random().toString(36).substr(2, 9);
        },

        // Gestion des niveaux de zoom
        minZoom: 5,
        maxZoom: 15,

        // Optimisations de performance
        rendererFactory: L.canvas.tile,
        tolerance: 0.5,
        
        // Debug si activé
        debug: debug
      };

      // Créer la couche vectorielle
      const vectorGrid = L.vectorGrid.protobuf(url, vectorTileOptions);
      
      // Événements de la couche
      vectorGrid.on('loading', () => {
        if (debug) console.log('🔄 Chargement des tuiles vectorielles...');
      });

      vectorGrid.on('load', () => {
        if (debug) console.log('✅ Tuiles vectorielles chargées');
        setStatus('ready');
      });

      vectorGrid.on('tileerror', (e) => {
        console.warn('⚠️ Erreur de chargement de tuile:', e.coords);
        if (debug) {
          console.log('URL de la tuile en erreur:', e.tile.src);
        }
      });

      // Gestion des clics
      vectorGrid.on('click', function(e) {
        if (debug) {
          console.log('🖱️ Clic sur tuile vectorielle:', e);
        }
        
        if (e.layer && e.layer.properties) {
          const props = e.layer.properties;
          let popupContent = createPopupContent(props);
          
          // Afficher le popup
          L.popup({
            maxWidth: 300,
            className: 'morocco-vector-popup'
          })
          .setLatLng(e.latlng)
          .setContent(popupContent)
          .openOn(map);
        }
      });

      // Gestion du survol
      vectorGrid.on('mouseover', function(e) {
        if (e.layer) {
          map.getContainer().style.cursor = 'pointer';
          
          // Effet de survol
          if (e.layer.setStyle) {
            e.layer.setStyle({
              weight: e.layer.options.weight + 1,
              opacity: Math.min(e.layer.options.opacity + 0.2, 1)
            });
          }
        }
      });

      vectorGrid.on('mouseout', function(e) {
        map.getContainer().style.cursor = '';
        
        // Restaurer le style original
        if (e.layer && e.layer.setStyle) {
          const originalStyle = vectorTileOptions.vectorTileLayerStyles[e.layer.properties?.layer] || 
                              vectorTileOptions.vectorTileLayerStyles[''];
          e.layer.setStyle(originalStyle);
        }
      });

      // Ajouter la couche à la carte
      vectorGrid.addTo(map);
      layerRef.current = vectorGrid;

      console.log('✅ Couche de tuiles vectorielles créée avec succès');
      setStatus('ready');

    } catch (error) {
      console.error('❌ Erreur lors de la création de la couche vectorielle:', error);
      setStatus('error');
      
      if (onError) {
        onError(error);
      }

      // Retry logic
      if (retryCount < maxRetries) {
        console.log(`🔄 Tentative ${retryCount + 1}/${maxRetries} dans 2 secondes...`);
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          setStatus('loading');
        }, 2000);
      }
    }
  };

  // Fonction pour créer le contenu du popup
  const createPopupContent = (properties) => {
    let content = '<div style="font-family: -apple-system, BlinkMacSystemFont, sans-serif; line-height: 1.4;">';
    
    // Titre principal
    if (properties.name || properties.NAME) {
      const name = properties.name || properties.NAME;
      content += `<h4 style="color: #1e40af; margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">${name}</h4>`;
    }
    
    // Nom en arabe si disponible
    if (properties.name_ar || properties.NAME_AR) {
      const nameAr = properties.name_ar || properties.NAME_AR;
      content += `<p style="direction: rtl; margin: 4px 0; font-size: 13px; color: #64748b;">${nameAr}</p>`;
    }
    
    // Type/catégorie
    if (properties.type || properties.TYPE || properties.fclass) {
      const type = properties.type || properties.TYPE || properties.fclass;
      content += `<p style="font-size: 12px; margin: 4px 0; color: #64748b;"><strong>Type:</strong> ${type}</p>`;
    }
    
    // Population si disponible
    if (properties.population || properties.POP) {
      const pop = properties.population || properties.POP;
      if (pop && pop > 0) {
        content += `<p style="font-size: 12px; margin: 4px 0; color: #64748b;"><strong>Population:</strong> ${parseInt(pop).toLocaleString()}</p>`;
      }
    }
    
    // Statut de capitale
    if (properties.capital === 'yes' || properties.CAPITAL === 'yes') {
      content += `<p style="color: #dc2626; font-weight: 600; font-size: 12px; margin: 4px 0;">🏛️ Capitale</p>`;
    }
    
    // Informations administratives
    if (properties.admin_level) {
      content += `<p style="font-size: 11px; margin: 4px 0; color: #9ca3af;">Niveau admin: ${properties.admin_level}</p>`;
    }
    
    content += '</div>';
    return content;
  };

  // Effet principal
  useEffect(() => {
    if (!map || !visible) return;

    const initializeLayer = async () => {
      try {
        setStatus('loading');
        
        // Charger leaflet.vectorgrid
        await loadVectorGridScript();
        
        // Créer la couche vectorielle
        await createVectorLayer();
        
      } catch (error) {
        console.error('❌ Erreur d\'initialisation:', error);
        setStatus('error');
        
        if (onError) {
          onError(error);
        }
      }
    };

    initializeLayer();

    // Nettoyage
    return () => {
      if (layerRef.current && map && map.hasLayer(layerRef.current)) {
        try {
          map.removeLayer(layerRef.current);
          layerRef.current = null;
          if (debug) console.log('🧹 Couche de tuiles vectorielles supprimée');
        } catch (error) {
          console.error('Erreur lors de la suppression de la couche:', error);
        }
      }
    };
  }, [map, url, visible, retryCount]);

  // Gestion de la visibilité
  useEffect(() => {
    if (layerRef.current && map) {
      if (visible) {
        if (!map.hasLayer(layerRef.current)) {
          map.addLayer(layerRef.current);
        }
      } else {
        if (map.hasLayer(layerRef.current)) {
          map.removeLayer(layerRef.current);
        }
      }
    }
  }, [visible, map]);

  // Retourner un indicateur de statut si en mode debug
  if (debug) {
    return (
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        zIndex: 1000,
        fontSize: '12px',
        fontFamily: 'monospace'
      }}>
        Tuiles vectorielles: {status}
        {retryCount > 0 && ` (tentative ${retryCount}/${maxRetries})`}
      </div>
    );
  }

  return null;
};

export default OfflineVectorTileLayer;