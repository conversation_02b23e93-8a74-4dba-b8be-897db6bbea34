import React, { useRef, useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Import des données géographiques
import moroccoGeometry from '../../data/morocco-complete.js';
import moroccoCities from '../../data/morocco-cities.js';

// Styles pour la carte
const MAP_STYLES = {
  country: {
    fillColor: '#e6f3ff',
    weight: 3,
    opacity: 1,
    color: '#1e40af',
    dashArray: '',
    fillOpacity: 0.1
  },
  cities: {
    capital: {
      radius: 12,
      fillColor: '#dc2626',
      color: '#ffffff',
      weight: 3,
      opacity: 1,
      fillOpacity: 0.9
    },
    city: {
      radius: 8,
      fillColor: '#3b82f6',
      color: '#ffffff',
      weight: 2,
      opacity: 1,
      fillOpacity: 0.8
    }
  }
};

// Configuration de la carte
const MAP_CONFIG = {
  center: [31.7917, -7.0926], // Centre du Maroc
  zoom: 6,
  minZoom: 5,
  maxZoom: 15,
  zoomControl: true,
  scrollWheelZoom: true,
  doubleClickZoom: true,
  dragging: true
};

const MoroccoOfflineMap = ({ onEquipmentClick, className = "" }) => {
  const mapRef = useRef(null);
  const [mapReady, setMapReady] = useState(false);

  // Gestionnaire pour quand la carte est prête
  const handleMapReady = () => {
    setMapReady(true);
    console.log('🗺️ Carte du Maroc prête (mode offline)');
  };

  // Style pour les villes selon leur type
  const getCityStyle = (feature) => {
    const isCapital = feature.properties.type === 'capital';
    return isCapital ? MAP_STYLES.cities.capital : MAP_STYLES.cities.city;
  };

  // Création des marqueurs pour les villes
  const pointToLayer = (feature, latlng) => {
    const style = getCityStyle(feature);
    return L.circleMarker(latlng, style);
  };

  // Popups pour les villes
  const onEachCityFeature = (feature, layer) => {
    if (feature.properties) {
      const props = feature.properties;
      
      let popupContent = `
        <div style="font-family: Inter, sans-serif; min-width: 200px;">
          <h3 style="color: #1e40af; margin: 0 0 8px 0; font-size: 16px;">
            ${props.name}
          </h3>
          <p style="direction: rtl; margin: 4px 0; color: #666; font-size: 14px;">
            ${props.name_ar}
          </p>
          <div style="border-top: 1px solid #e5e7eb; padding-top: 8px; margin-top: 8px;">
            <p style="margin: 2px 0; font-size: 12px;">
              <strong>Région:</strong> ${props.region}
            </p>
            <p style="margin: 2px 0; font-size: 12px;">
              <strong>Population:</strong> ${props.population.toLocaleString()}
            </p>
            ${props.type === 'capital' ? 
              '<p style="color: #dc2626; font-weight: bold; margin: 4px 0;">🏛️ Capitale du Royaume</p>' : 
              ''
            }
          </div>
        </div>
      `;
      
      layer.bindPopup(popupContent, {
        maxWidth: 300,
        closeButton: true
      });
      
      // Tooltip au survol
      layer.bindTooltip(props.name, {
        permanent: false,
        direction: 'top',
        offset: [0, -10]
      });
    }
  };

  // Popup pour le pays
  const onEachCountryFeature = (feature, layer) => {
    if (feature.properties) {
      const props = feature.properties;
      
      let popupContent = `
        <div style="font-family: Inter, sans-serif; min-width: 250px;">
          <h3 style="color: #1e40af; margin: 0 0 8px 0; font-size: 18px;">
            ${props.name}
          </h3>
          <p style="direction: rtl; margin: 4px 0; color: #666; font-size: 16px;">
            ${props.name_ar}
          </p>
          <div style="border-top: 1px solid #e5e7eb; padding-top: 8px; margin-top: 8px;">
            <p style="margin: 2px 0; font-size: 12px;">
              <strong>Capitale:</strong> ${props.capital}
            </p>
            <p style="margin: 2px 0; font-size: 12px;">
              <strong>Population:</strong> ${props.population.toLocaleString()} habitants
            </p>
            <p style="margin: 2px 0; font-size: 12px;">
              <strong>Superficie:</strong> ${props.area_km2.toLocaleString()} km²
            </p>
            <p style="margin: 2px 0; font-size: 12px;">
              <strong>Monnaie:</strong> ${props.currency}
            </p>
          </div>
        </div>
      `;
      
      layer.bindPopup(popupContent, {
        maxWidth: 350,
        closeButton: true
      });
    }
  };

  return (
    <div className={`relative w-full h-full ${className}`}>
      <MapContainer
        ref={mapRef}
        center={MAP_CONFIG.center}
        zoom={MAP_CONFIG.zoom}
        minZoom={MAP_CONFIG.minZoom}
        maxZoom={MAP_CONFIG.maxZoom}
        zoomControl={MAP_CONFIG.zoomControl}
        scrollWheelZoom={MAP_CONFIG.scrollWheelZoom}
        doubleClickZoom={MAP_CONFIG.doubleClickZoom}
        dragging={MAP_CONFIG.dragging}
        className="w-full h-full"
        whenReady={handleMapReady}
      >
        {/* Couche de fond offline (transparente) */}
        <TileLayer
          url="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
          attribution="© Royaume du Maroc - C2-EW Platform"
          maxZoom={18}
        />

        {/* Territoire du Royaume du Maroc */}
        <GeoJSON
          data={moroccoGeometry}
          style={MAP_STYLES.country}
          onEachFeature={onEachCountryFeature}
        />

        {/* Villes principales */}
        <GeoJSON
          data={moroccoCities}
          pointToLayer={pointToLayer}
          onEachFeature={onEachCityFeature}
        />
      </MapContainer>

      {/* Indicateur de statut */}
      {mapReady && (
        <div className="absolute bottom-4 left-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium z-50">
          🇲🇦 Mode Offline - Royaume du Maroc
        </div>
      )}
    </div>
  );
};

export default MoroccoOfflineMap;
