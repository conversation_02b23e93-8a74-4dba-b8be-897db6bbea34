#!/usr/bin/env python3
"""
Serveur backend simple pour C2-EW Platform
Utilise des données mockées pour le développement
"""

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import json
from datetime import datetime, timedelta
import jwt
import asyncio

# Configuration
SECRET_KEY = "dev-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

app = FastAPI(
    title="C2-EW Platform API",
    description="API pour la plateforme de Commandement et Contrôle - Guerre Électronique",
    version="1.0.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

security = HTTPBearer()

# Modèles Pydantic
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class User(BaseModel):
    id: int
    username: str
    email: str
    full_name: str
    role: str
    is_active: bool

class Equipment(BaseModel):
    id: int
    name: str
    type: str
    status: str
    description: str
    location: Dict[str, float]
    metrics: Optional[Dict[str, Any]] = None

class Alert(BaseModel):
    id: int
    title: str
    message: str
    level: str
    status: str
    timestamp: str
    equipment_id: Optional[int] = None
    location: Optional[Dict[str, float]] = None

class Plugin(BaseModel):
    id: str
    name: str
    display_name: str
    version: str
    description: str
    status: str
    author: Optional[str] = None
    supported_equipment_types: List[str] = []
    commands: List[Dict[str, Any]] = []

# Données mockées
MOCK_USERS = {
    "admin": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Administrateur",
        "role": "admin",
        "password": "admin123",
        "is_active": True
    },
    "operator": {
        "id": 2,
        "username": "operator",
        "email": "<EMAIL>",
        "full_name": "Opérateur",
        "role": "operator",
        "password": "op123",
        "is_active": True
    },
    "technician": {
        "id": 3,
        "username": "technician",
        "email": "<EMAIL>",
        "full_name": "Technicien",
        "role": "technician",
        "password": "tech123",
        "is_active": True
    },
    "viewer": {
        "id": 4,
        "username": "viewer",
        "email": "<EMAIL>",
        "full_name": "Observateur",
        "role": "viewer",
        "password": "view123",
        "is_active": True
    }
}

MOCK_EQUIPMENT = [
    {
        "id": 1,
        "name": "ICOM IC-R8600",
        "type": "comint",
        "status": "active",
        "description": "Récepteur de communication large bande",
        "location": {"latitude": 48.8566, "longitude": 2.3522},
        "metrics": {
            "frequency": "145.500 MHz",
            "signal_strength": "-65 dBm",
            "mode": "FM",
            "bandwidth": "12.5 kHz"
        }
    },
    {
        "id": 2,
        "name": "Anti-Drone System",
        "type": "anti_drone",
        "status": "standby",
        "description": "Système de détection et neutralisation de drones",
        "location": {"latitude": 48.8606, "longitude": 2.3376},
        "metrics": {
            "detection_range": "2 km",
            "targets_tracked": 0,
            "jamming_power": "50W"
        }
    },
    {
        "id": 3,
        "name": "ELINT Sensor",
        "type": "elint",
        "status": "active",
        "description": "Capteur de renseignement électronique",
        "location": {"latitude": 48.8534, "longitude": 2.3488},
        "metrics": {
            "frequency_range": "1-18 GHz",
            "signals_detected": 15,
            "analysis_mode": "Auto"
        }
    }
]

MOCK_ALERTS = [
    {
        "id": 1,
        "title": "Signal suspect détecté",
        "message": "Fréquence 446.000 MHz - Signal non identifié",
        "level": "warning",
        "status": "unread",
        "timestamp": datetime.now().isoformat(),
        "equipment_id": 1,
        "location": {"latitude": 48.8566, "longitude": 2.3522}
    },
    {
        "id": 2,
        "title": "Système anti-drone activé",
        "message": "Détection automatique d'un drone hostile",
        "level": "critical",
        "status": "unread",
        "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat(),
        "equipment_id": 2,
        "location": {"latitude": 48.8606, "longitude": 2.3376}
    }
]

MOCK_PLUGINS = [
    {
        "id": "icom-r8600",
        "name": "icom-r8600",
        "display_name": "ICOM IC-R8600",
        "version": "1.0.0",
        "description": "Plugin pour récepteur ICOM IC-R8600",
        "status": "active",
        "author": "C2-EW Team",
        "supported_equipment_types": ["comint"],
        "commands": [
            {"name": "set_frequency", "description": "Changer la fréquence"},
            {"name": "set_mode", "description": "Changer le mode de réception"},
            {"name": "start_recording", "description": "Démarrer l'enregistrement"}
        ]
    }
]

# Fonctions utilitaires
def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="Token invalide")
        return username
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Token invalide")

def get_current_user(username: str = Depends(verify_token)):
    user = MOCK_USERS.get(username)
    if user is None:
        raise HTTPException(status_code=404, detail="Utilisateur non trouvé")
    return User(**{k: v for k, v in user.items() if k != 'password'})

# Routes d'authentification
@app.post("/api/v1/auth/login", response_model=Token)
async def login(login_data: LoginRequest):
    user = MOCK_USERS.get(login_data.username)
    if not user or user["password"] != login_data.password:
        raise HTTPException(status_code=401, detail="Identifiants incorrects")

    access_token = create_access_token(data={"sub": user["username"]})
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/v1/auth/me", response_model=User)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    return current_user

@app.post("/api/v1/auth/refresh")
async def refresh_token():
    # Pour le moment, on retourne juste une erreur car on n'a pas de refresh token
    raise HTTPException(status_code=404, detail="Refresh token non implémenté")

@app.post("/api/v1/auth/logout")
async def logout():
    # Pour le moment, on retourne juste un succès
    return {"message": "Déconnexion réussie"}

# Routes des équipements
@app.get("/api/v1/equipment", response_model=List[Equipment])
async def get_equipment(current_user: User = Depends(get_current_user)):
    return [Equipment(**eq) for eq in MOCK_EQUIPMENT]

@app.get("/api/v1/equipment/{equipment_id}", response_model=Equipment)
async def get_equipment_by_id(equipment_id: int, current_user: User = Depends(get_current_user)):
    equipment = next((eq for eq in MOCK_EQUIPMENT if eq["id"] == equipment_id), None)
    if not equipment:
        raise HTTPException(status_code=404, detail="Équipement non trouvé")
    return Equipment(**equipment)

# Routes des alertes
@app.get("/api/v1/alerts", response_model=List[Alert])
async def get_alerts(current_user: User = Depends(get_current_user)):
    return [Alert(**alert) for alert in MOCK_ALERTS]

# Route des logs (pour les alertes)
@app.get("/api/v1/logs")
async def get_logs(current_user: User = Depends(get_current_user)):
    return {
        "logs": [
            {
                "id": 1,
                "timestamp": "2024-01-15T10:30:00Z",
                "level": "INFO",
                "message": "Système C2-EW initialisé",
                "source": "system"
            },
            {
                "id": 2,
                "timestamp": "2024-01-15T10:31:00Z",
                "level": "WARNING",
                "message": "Signal COMINT détecté",
                "source": "comint"
            },
            {
                "id": 3,
                "timestamp": "2024-01-15T10:32:00Z",
                "level": "ERROR",
                "message": "Perte de connexion équipement #123",
                "source": "equipment"
            }
        ],
        "total": 3
    }

# Route des équipements par type (pour le store)
@app.get("/equipment")
async def get_equipment_by_type(equipment_type: str = None, current_user: User = Depends(get_current_user)):
    if equipment_type:
        filtered_equipment = [eq for eq in MOCK_EQUIPMENT if eq["type"] == equipment_type]
        return [Equipment(**eq) for eq in filtered_equipment]
    return [Equipment(**eq) for eq in MOCK_EQUIPMENT]

# Routes des plugins
@app.get("/api/v1/plugins", response_model=List[Plugin])
async def get_plugins(current_user: User = Depends(get_current_user)):
    return [Plugin(**plugin) for plugin in MOCK_PLUGINS]

# Route de santé
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "database": "mock",
        "environment": "development"
    }

# Gestionnaire WebSocket simple
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Connexion fermée, on la supprime
                self.active_connections.remove(connection)

manager = ConnectionManager()

# Route WebSocket
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    await manager.connect(websocket)
    try:
        # Envoyer un message de bienvenue
        await manager.send_personal_message(json.dumps({
            "type": "connection_established",
            "data": {
                "message": "Connexion WebSocket établie",
                "timestamp": datetime.utcnow().isoformat()
            }
        }), websocket)

        while True:
            # Recevoir les messages du client
            data = await websocket.receive_text()
            message = json.loads(data)

            # Echo du message pour test
            await manager.send_personal_message(json.dumps({
                "type": "echo",
                "data": message
            }), websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Route racine
@app.get("/")
async def root():
    return {
        "message": "C2-EW Platform API",
        "version": "1.0.0",
        "docs": "/docs"
    }

if __name__ == "__main__":
    print("🚀 Démarrage du serveur backend C2-EW...")
    print("📡 API disponible sur: http://localhost:8000")
    print("📚 Documentation sur: http://localhost:8000/docs")
    print("🔒 Comptes de test:")
    print("   - admin / admin123")
    print("   - operator / op123")
    print("   - technician / tech123")
    print("   - viewer / view123")
    
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
