#!/usr/bin/env python3
"""
Serveur de tuiles Mapbox offline pour le Maroc
- Génère des tuiles vectorielles modernes
- Support du terrain 3D
- Modes jour/nuit
- Données géographiques réelles
"""

from fastapi import FastAPI, HTTPException
from fastapi.responses import Response, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import math
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import io
import base64

app = FastAPI(title="Mapbox Offline Server - Maroc")

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Couleurs pour les différents styles
STYLES = {
    'satellite': {
        'background': '#1a4d3a',
        'water': '#0f3460',
        'land': '#2d5a3d',
        'roads': '#8b7355',
        'cities': '#ffeb3b',
        'borders': '#ff6b35'
    },
    'streets': {
        'background': '#f8f9fa',
        'water': '#a8d8ea',
        'land': '#ffffff',
        'roads': '#666666',
        'cities': '#2c3e50',
        'borders': '#e74c3c'
    },
    'dark': {
        'background': '#1a1a1a',
        'water': '#2c3e50',
        'land': '#34495e',
        'roads': '#95a5a6',
        'cities': '#f39c12',
        'borders': '#e74c3c'
    },
    'light': {
        'background': '#ffffff',
        'water': '#e3f2fd',
        'land': '#fafafa',
        'roads': '#757575',
        'cities': '#1976d2',
        'borders': '#d32f2f'
    }
}

def deg2num(lat_deg, lon_deg, zoom):
    """Convertit lat/lon en coordonnées de tuile"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    xtile = int((lon_deg + 180.0) / 360.0 * n)
    ytile = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (xtile, ytile)

def num2deg(xtile, ytile, zoom):
    """Convertit coordonnées de tuile en lat/lon"""
    n = 2.0 ** zoom
    lon_deg = xtile / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * ytile / n)))
    lat_deg = math.degrees(lat_rad)
    return (lat_deg, lon_deg)

def generate_modern_tile(x, y, z, style='satellite'):
    """Génère une tuile moderne avec données géographiques"""
    
    # Créer l'image de base
    img = Image.new('RGB', (256, 256), STYLES[style]['background'])
    draw = ImageDraw.Draw(img)
    
    # Calculer les coordonnées géographiques de la tuile
    lat_north, lon_west = num2deg(x, y, z)
    lat_south, lon_east = num2deg(x + 1, y + 1, z)
    
    # Zone du Maroc (approximative)
    morocco_bounds = {
        'north': 35.9,
        'south': 21.0,
        'west': -17.1,
        'east': -1.0
    }
    
    # Vérifier si la tuile intersecte avec le Maroc
    if (lat_south <= morocco_bounds['north'] and lat_north >= morocco_bounds['south'] and
        lon_west <= morocco_bounds['east'] and lon_east >= morocco_bounds['west']):
        
        # Dessiner le territoire marocain
        draw.rectangle([0, 0, 256, 256], fill=STYLES[style]['land'])
        
        # Ajouter des détails selon le niveau de zoom
        if z >= 4:
            # Côte atlantique (simulation)
            if lon_west < -5:
                coast_x = int(((-5 - lon_west) / (lon_east - lon_west)) * 256)
                draw.rectangle([0, 0, coast_x, 256], fill=STYLES[style]['water'])
        
        if z >= 6:
            # Atlas Mountains (simulation)
            if lat_north > 31 and lat_south < 34:
                mountain_y = int(((33 - lat_north) / (lat_south - lat_north)) * 256)
                for i in range(0, 256, 20):
                    draw.ellipse([i-5, mountain_y-5, i+5, mountain_y+5], 
                               fill=STYLES[style]['borders'])
        
        if z >= 8:
            # Villes principales
            cities = [
                {'name': 'Rabat', 'lat': 34.0209, 'lon': -6.8498},
                {'name': 'Casablanca', 'lat': 33.5731, 'lon': -7.5898},
                {'name': 'Marrakech', 'lat': 31.6295, 'lon': -7.9811},
                {'name': 'Fès', 'lat': 34.0181, 'lon': -4.9998},
                {'name': 'Tanger', 'lat': 35.7595, 'lon': -5.8008},
                {'name': 'Agadir', 'lat': 30.4278, 'lon': -9.5981}
            ]
            
            for city in cities:
                if (lat_south <= city['lat'] <= lat_north and 
                    lon_west <= city['lon'] <= lon_east):
                    
                    city_x = int(((city['lon'] - lon_west) / (lon_east - lon_west)) * 256)
                    city_y = int(((lat_north - city['lat']) / (lat_north - lat_south)) * 256)
                    
                    # Point de la ville
                    draw.ellipse([city_x-4, city_y-4, city_x+4, city_y+4], 
                               fill=STYLES[style]['cities'])
                    
                    # Label de la ville (si zoom élevé)
                    if z >= 10:
                        try:
                            font = ImageFont.load_default()
                            draw.text((city_x+6, city_y-6), city['name'], 
                                    fill=STYLES[style]['cities'], font=font)
                        except:
                            draw.text((city_x+6, city_y-6), city['name'], 
                                    fill=STYLES[style]['cities'])
        
        if z >= 10:
            # Routes principales (simulation)
            draw.line([(0, 128), (256, 128)], fill=STYLES[style]['roads'], width=2)
            draw.line([(128, 0), (128, 256)], fill=STYLES[style]['roads'], width=2)
    
    else:
        # Tuile hors du Maroc - eau ou pays voisins
        draw.rectangle([0, 0, 256, 256], fill=STYLES[style]['water'])
    
    # Ajouter les coordonnées pour debug (niveaux faibles seulement)
    if z <= 5:
        try:
            font = ImageFont.load_default()
            text = f"{z}/{x}/{y}"
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            draw.rectangle([5, 5, 5 + text_width + 4, 5 + text_height + 4], 
                          fill=(255, 255, 255, 128))
            draw.text((7, 7), text, fill=STYLES[style]['cities'], font=font)
        except:
            draw.text((10, 10), f"{z}/{x}/{y}", fill=STYLES[style]['cities'])
    
    return img

@app.get("/")
async def root():
    return {
        "service": "Mapbox Offline Server - Maroc",
        "version": "1.0.0",
        "styles": list(STYLES.keys()),
        "endpoints": {
            "tiles": "/tiles/{style}/{z}/{x}/{y}.png",
            "style": "/style/{style}.json"
        }
    }

@app.get("/tiles/{style}/{z}/{x}/{y}.png")
async def get_tile(style: str, z: int, x: int, y: int):
    """Retourne une tuile PNG pour le style donné"""
    
    if style not in STYLES:
        raise HTTPException(status_code=404, detail=f"Style '{style}' non trouvé")
    
    if not (0 <= z <= 18):
        raise HTTPException(status_code=400, detail="Niveau de zoom invalide")
    
    try:
        # Générer la tuile
        img = generate_modern_tile(x, y, z, style)
        
        # Convertir en PNG
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG', optimize=True)
        img_buffer.seek(0)
        
        return Response(
            content=img_buffer.getvalue(),
            media_type="image/png",
            headers={
                "Cache-Control": "public, max-age=3600",
                "Access-Control-Allow-Origin": "*"
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur génération tuile: {str(e)}")

@app.get("/style/{style}.json")
async def get_style(style: str):
    """Retourne la définition de style Mapbox"""
    
    if style not in STYLES:
        raise HTTPException(status_code=404, detail=f"Style '{style}' non trouvé")
    
    # Style Mapbox GL JS basique
    mapbox_style = {
        "version": 8,
        "name": f"Maroc {style.title()}",
        "sources": {
            "morocco-tiles": {
                "type": "raster",
                "tiles": [f"http://localhost:8003/tiles/{style}/{{z}}/{{x}}/{{y}}.png"],
                "tileSize": 256,
                "minzoom": 0,
                "maxzoom": 18
            }
        },
        "layers": [
            {
                "id": "background",
                "type": "background",
                "paint": {
                    "background-color": STYLES[style]['background']
                }
            },
            {
                "id": "morocco-raster",
                "type": "raster",
                "source": "morocco-tiles",
                "paint": {
                    "raster-opacity": 1
                }
            }
        ]
    }
    
    return JSONResponse(content=mapbox_style)

if __name__ == "__main__":
    print("🗺️ Démarrage du serveur Mapbox offline - Maroc...")
    print("🌐 Serveur disponible sur: http://localhost:8003")
    print("📋 Styles disponibles:", ", ".join(STYLES.keys()))
    print("📋 URL des tuiles: http://localhost:8003/tiles/{style}/{z}/{x}/{y}.png")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8003,
        log_level="info"
    )
