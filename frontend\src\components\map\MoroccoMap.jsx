import React, { useRef, useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { motion } from 'framer-motion';

// Import des composants
import MapControls from './MapControls';
import EquipmentMarkers from './EquipmentMarkers';
import DrawingTools from './DrawingTools';

// Fix pour les icônes Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: '/leaflet-marker-icon-2x.png',
  iconUrl: '/leaflet-marker-icon.png',
  shadowUrl: '/leaflet-marker-shadow.png',
});

// Configuration de la carte pour le Maroc
const MOROCCO_CONFIG = {
  center: [31.7917, -7.0926], // Centre du Maroc
  zoom: 6,
  minZoom: 5,
  maxZoom: 15,
  bounds: [
    [20.5, -17.5], // Sud-Ouest (incluant provinces du Sud)
    [36.0, -0.5]   // Nord-Est
  ]
};

const MoroccoMap = ({ onEquipmentClick, className = "" }) => {
  const mapRef = useRef(null);
  const [mapReady, setMapReady] = useState(false);
  const [mapLayer, setMapLayer] = useState('offline'); // Toujours offline

  const handleMapReady = () => {
    setMapReady(true);
    console.log('🗺️ Carte offline du Maroc prête');
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`relative w-full h-full ${className}`}
    >
      <MapContainer
        ref={mapRef}
        center={MOROCCO_CONFIG.center}
        zoom={MOROCCO_CONFIG.zoom}
        minZoom={MOROCCO_CONFIG.minZoom}
        maxZoom={MOROCCO_CONFIG.maxZoom}
        maxBounds={MOROCCO_CONFIG.bounds}
        maxBoundsViscosity={1.0}
        zoomControl={false}
        className="w-full h-full"
        whenReady={handleMapReady}
      >
        {/* Tuiles offline professionnelles */}
        <TileLayer
          url="http://localhost:8002/tiles/{z}/{x}/{y}.png"
          attribution="© C2-EW Platform - Mode Offline"
          maxZoom={15}
          className="map-tiles"
        />

        {/* Marqueurs d'équipements */}
        <EquipmentMarkers onEquipmentClick={onEquipmentClick} />

        {/* Outils de dessin */}
        {mapReady && <DrawingTools />}
      </MapContainer>

      {/* Contrôles de la carte */}
      <MapControls
        className="absolute top-4 right-4 z-30"
        mapLayer={mapLayer}
        setMapLayer={setMapLayer}
      />

      {/* Indicateur offline */}
      {mapReady && (
        <div className="absolute bottom-4 right-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium z-50">
          🇲🇦 Offline - Royaume du Maroc
        </div>
      )}
    </motion.div>
  );
};

export default MoroccoMap;
