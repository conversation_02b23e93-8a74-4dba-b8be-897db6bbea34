import React from 'react';
import MoroccoOfflineMap from './MoroccoOfflineMap';
import MapTools from './MapTools';

const MoroccoMap = ({ onEquipmentClick, className = "" }) => {
  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Carte offline du Maroc */}
      <MoroccoOfflineMap 
        onEquipmentClick={onEquipmentClick}
        className="w-full h-full"
      />
      
      {/* Outils de carte */}
      <MapTools className="absolute top-4 right-4 z-50" />
    </div>
  );
};

export default MoroccoMap;
