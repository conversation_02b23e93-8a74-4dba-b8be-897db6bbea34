import React, { useEffect, useRef, useState } from 'react';
import { MapContainer as LeafletMapContainer, TileLayer, useMap, GeoJSON } from 'react-leaflet';
import { motion } from 'framer-motion';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

import EquipmentMarker from './EquipmentMarker';
import DrawingTools from './DrawingTools';
import MapControls from './MapControls';
import OfflineTileLayer from './OfflineTileLayer';
import { useEquipmentStore } from '@/store/equipmentStore';
import { useMapStore } from '@/store/mapStore';

// Fix pour les icônes Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Configuration optimisée pour le Maroc
const MOROCCO_CONFIG = {
  center: [29.0, -8.0],
  zoom: 6,
  minZoom: 5,
  maxZoom: 15,
  bounds: [
    [20.5, -17.5],
    [36.0, -0.5]
  ]
};

// Données GeoJSON précises du Maroc (frontières officielles complètes)
const MOROCCO_GEOJSON = {
  type: 'Feature',
  properties: {
    name: 'Royaume du Maroc',
    name_ar: 'المملكة المغربية',
    capital: 'Rabat'
  },
  
};

// Styles pour les couches de carte
const MAP_STYLES = {
  morocco: {
    color: '#1e40af',
    weight: 3,
    opacity: 0.8,
    fillColor: 'transparent',
    fillOpacity: 0
  },
  equipment: {
    radius: 8,
    fillColor: '#3b82f6',
    color: '#ffffff',
    weight: 2,
    opacity: 1,
    fillOpacity: 0.8
  }
};

const MapEventHandler = ({ onEquipmentClick, onMapClick }) => {
  const map = useMap();

  useEffect(() => {
    const handleClick = (e) => {
      if (onMapClick) {
        onMapClick(e);
      }
    };

    map.on('click', handleClick);
    return () => map.off('click', handleClick);
  }, [map, onMapClick]);

  return null;
};

const MapViewController = () => {
  const map = useMap();
  const { setMapInstance } = useMapStore();

  useEffect(() => {
    if (map) {
      setMapInstance(map);
      map.fitBounds(MOROCCO_CONFIG.bounds, {
        padding: [20, 20],
        maxZoom: MOROCCO_CONFIG.zoom
      });
    }
  }, [map, setMapInstance]);

  return null;
};

const MoroccoMap = ({ onEquipmentClick, className }) => {
  const mapRef = useRef(null);
  const [mapReady, setMapReady] = useState(false);
  const [mapLayer, setMapLayer] = useState('street'); // 'street', 'satellite', 'hybrid'
  // Utiliser le GeoJSON simple pour offline
  const showVectorTiles = false;

  const { equipment, loading: equipmentLoading } = useEquipmentStore();
  const { activeTool, setMapInstance, mapCenter, mapZoom } = useMapStore();

  const mapConfig = {
    center: mapCenter || MOROCCO_CONFIG.center,
    zoom: mapZoom || MOROCCO_CONFIG.zoom,
    minZoom: MOROCCO_CONFIG.minZoom,
    maxZoom: MOROCCO_CONFIG.maxZoom,
    zoomControl: false,
    attributionControl: false,
    maxBounds: MOROCCO_CONFIG.bounds,
    maxBoundsViscosity: 1.0
  };

  const handleMapReady = () => {
    setMapReady(true);
    if (mapRef.current) {
      setMapInstance(mapRef.current);
    }
  };

  const handleEquipmentMarkerClick = (equipment) => {
    if (onEquipmentClick) {
      onEquipmentClick(equipment);
    }
  };

  const handleMapClick = (e) => {
    console.log('Clic sur la carte:', e.latlng);
  };

  if (equipmentLoading) {
    return (
      <div className={`flex items-center justify-center bg-c2-gray-400 ${className}`}>
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-8 h-8 border-2 border-c2-blue border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className={`relative w-full h-full ${className}`}
    >
      <LeafletMapContainer
        ref={mapRef}
        {...mapConfig}
        className="w-full h-full bg-c2-gray-400"
        whenReady={handleMapReady}
      >
        {/* Couche de fond offline simple */}
        <TileLayer
          url="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
          attribution="© C2-EW Platform - Mode Offline"
          maxZoom={18}
        />

        {/* Tuiles offline du Maroc */}
        {showVectorTiles && (
          <OfflineTileLayer
            url="http://localhost:8001/tiles/{z}/{x}/{y}.json"
            visible={showVectorTiles}
          />
        )}



        {/* Frontières du Maroc (fallback si pas de tuiles vectorielles) */}
        {!showVectorTiles && (
          <GeoJSON
            data={MOROCCO_GEOJSON}
            style={MAP_STYLES.morocco}
            onEachFeature={(feature, layer) => {
              layer.bindTooltip(
                `<strong>${feature.properties.name}</strong><br/>
                 <span style="direction: rtl;">${feature.properties.name_ar}</span>`,
                {
                  permanent: false,
                  direction: 'top',
                  className: 'morocco-tooltip'
                }
              );
            }}
          />
        )}
        <MapEventHandler
          onEquipmentClick={handleEquipmentMarkerClick}
          onMapClick={handleMapClick}
        />
        <MapViewController />
        {equipment.map((item) => (
          <EquipmentMarker
            key={item.id}
            equipment={item}
            onClick={handleEquipmentMarkerClick}
          />
        ))}
        {mapReady && (
          <DrawingTools
            activeTool={activeTool}
            map={mapRef.current}
          />
        )}
      </LeafletMapContainer>
      <MapControls
        className="absolute top-4 right-4 z-30"
        mapLayer={mapLayer}
        setMapLayer={setMapLayer}
      />
      
      {equipment.length > 0 && (
        <div className="absolute top-4 left-4 bg-c2-gray-400/90 backdrop-blur-sm p-3 rounded-lg border border-c2-gray-300 text-c2-white max-w-xs z-30">
          <h4 className="text-sm font-bold mb-2">Équipements C2-EW</h4>
          <div className="text-xs space-y-1">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-c2-blue rounded-full border border-white"></div>
              <span>Équipements actifs</span>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default MoroccoMap;