import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const MapTools = ({ className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTool, setActiveTool] = useState(null);

  const tools = [
    {
      id: 'measure',
      name: 'Mesure',
      icon: '📏',
      description: 'Mesurer distances et surfaces'
    },
    {
      id: 'marker',
      name: 'Mar<PERSON>ur',
      icon: '📍',
      description: 'Ajouter des marqueurs'
    },
    {
      id: 'zone',
      name: 'Zone',
      icon: '🔲',
      description: 'Délimiter des zones'
    },
    {
      id: 'equipment',
      name: 'Équipement',
      icon: '📡',
      description: 'Déployer équipements'
    },
    {
      id: 'route',
      name: 'Itinéraire',
      icon: '🛣️',
      description: 'Tracer des routes'
    },
    {
      id: 'analysis',
      name: 'Analyse',
      icon: '📊',
      description: 'Outils d\'analyse'
    }
  ];

  const handleToolClick = (toolId) => {
    setActiveTool(activeTool === toolId ? null : toolId);
    console.log(`🔧 Outil ${toolId} ${activeTool === toolId ? 'désactivé' : 'activé'}`);
  };

  return (
    <div className={`absolute top-4 right-4 z-50 ${className}`}>
      {/* Bouton principal */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-c2-dark-100 hover:bg-c2-dark-200 text-c2-white p-3 rounded-lg shadow-lg border border-c2-gray-200"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <div className="flex items-center space-x-2">
          <span className="text-xl">🛠️</span>
          <span className="font-medium">Outils</span>
          <motion.span
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            ▼
          </motion.span>
        </div>
      </motion.button>

      {/* Panel des outils */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-16 right-0 bg-c2-dark-100 rounded-lg shadow-xl border border-c2-gray-200 p-4 min-w-[280px]"
          >
            <h3 className="text-c2-white font-bold mb-3 text-center">
              🗺️ Outils de Carte
            </h3>
            
            <div className="grid grid-cols-2 gap-2">
              {tools.map((tool) => (
                <motion.button
                  key={tool.id}
                  onClick={() => handleToolClick(tool.id)}
                  className={`p-3 rounded-lg border transition-all duration-200 ${
                    activeTool === tool.id
                      ? 'bg-c2-blue text-white border-c2-blue'
                      : 'bg-c2-dark-200 text-c2-white border-c2-gray-200 hover:bg-c2-gray-100'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-1">{tool.icon}</div>
                    <div className="font-medium text-sm">{tool.name}</div>
                    <div className="text-xs opacity-75 mt-1">
                      {tool.description}
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>

            {/* Informations sur l'outil actif */}
            {activeTool && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 p-3 bg-c2-blue rounded-lg"
              >
                <div className="text-white text-sm">
                  <strong>Outil actif:</strong> {tools.find(t => t.id === activeTool)?.name}
                </div>
                <div className="text-blue-100 text-xs mt-1">
                  Cliquez sur la carte pour utiliser cet outil
                </div>
              </motion.div>
            )}

            {/* Actions rapides */}
            <div className="mt-4 pt-3 border-t border-c2-gray-200">
              <div className="flex space-x-2">
                <button className="flex-1 bg-c2-gray-100 hover:bg-c2-gray-200 text-c2-white py-2 px-3 rounded text-sm">
                  🧹 Effacer
                </button>
                <button className="flex-1 bg-c2-gray-100 hover:bg-c2-gray-200 text-c2-white py-2 px-3 rounded text-sm">
                  💾 Sauver
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MapTools;
