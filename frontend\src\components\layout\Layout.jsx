import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Navbar from './Navbar';
import <PERSON>barLeft from './SidebarLeft';
import SidebarRight from './SidebarRight';
import SidebarBottom from './SidebarBottom';
import ModernMoroccoMap from '@/components/map/ModernMoroccoMap';
import EquipmentInterface from '@/components/equipment/EquipmentInterface';
import { useEquipmentStore } from '@/store/equipmentStore';
import { useMapStore } from '@/store/mapStore';
import { LAYOUT } from '@/utils/constants';

const Layout = () => {
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(false);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(false);
  const [bottomSidebarOpen, setBottomSidebarOpen] = useState(false);
  const [selectedEquipmentId, setSelectedEquipmentId] = useState(null);
  const [showEquipmentInterface, setShowEquipmentInterface] = useState(false);

  const { selectedEquipment } = useEquipmentStore();
  const { mapInstance } = useMapStore();

  // Gérer la sélection d'équipement depuis la sidebar gauche
  const handleEquipmentSelect = (equipmentId) => {
    setSelectedEquipmentId(equipmentId);
    setRightSidebarOpen(true);
  };

  // Gérer le clic sur un équipement sur la carte
  const handleMapEquipmentClick = (equipment) => {
    setSelectedEquipmentId(equipment.id);
    setShowEquipmentInterface(true);
  };

  // Calculer les dimensions de la carte
  const getMapDimensions = () => {
    const marginLeft = leftSidebarOpen ? LAYOUT.SIDEBAR_WIDTH : LAYOUT.SIDEBAR_COLLAPSED_WIDTH;
    const marginRight = rightSidebarOpen ? LAYOUT.SIDEBAR_WIDTH : 0;
    const marginBottom = bottomSidebarOpen ? LAYOUT.SIDEBAR_BOTTOM_HEIGHT : 0;

    return {
      width: `calc(100% - ${marginLeft + marginRight}px)`,
      height: `calc(100vh - ${LAYOUT.NAVBAR_HEIGHT + marginBottom}px)`,
      marginLeft: `${marginLeft}px`,
      marginRight: `${marginRight}px`,
    };
  };

  const mapStyle = getMapDimensions();

  // Appeler invalidateSize lorsque les dimensions changent
  useEffect(() => {
    if (mapInstance) {
      setTimeout(() => {
        mapInstance.invalidateSize();
      }, 300); // Attendre la fin de l'animation (correspond à la durée de transition)
    }
  }, [mapInstance, leftSidebarOpen, rightSidebarOpen, bottomSidebarOpen]);

  return (
    <div className="h-screen w-screen bg-c2-black overflow-hidden">
      {/* Navbar fixe */}
      <Navbar />

      {/* Container principal */}
      <div className="relative flex h-[calc(100vh-64px)]">
        {/* Sidebar gauche */}
        <SidebarLeft
          isOpen={leftSidebarOpen}
          onToggle={setLeftSidebarOpen}
          onEquipmentSelect={handleEquipmentSelect}
        />

        {/* Zone centrale - Carte */}
        <motion.div
          className="relative bg-c2-gray-400 z-0"
          style={mapStyle}
          animate={{
            marginLeft: mapStyle.marginLeft,
            marginRight: mapStyle.marginRight,
            width: mapStyle.width,
            height: mapStyle.height,
          }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
          <ModernMoroccoMap
            onEquipmentClick={handleMapEquipmentClick}
            className="w-full h-full"
          />

          {/* Interface d'équipement centrale (overlay) */}
          <AnimatePresence>
            {showEquipmentInterface && selectedEquipmentId && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3 }}
                className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40"
                onClick={() => setShowEquipmentInterface(false)}
              >
                <motion.div
                  className="bg-c2-gray-300 border border-c2-gray-200 rounded-lg shadow-2xl max-w-6xl max-h-[90vh] overflow-hidden"
                  onClick={(e) => e.stopPropagation()}
                  initial={{ y: 50 }}
                  animate={{ y: 0 }}
                  exit={{ y: 50 }}
                >
                  <EquipmentInterface
                    equipmentId={selectedEquipmentId}
                    onClose={() => setShowEquipmentInterface(false)}
                  />
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Sidebar droite */}
        <SidebarRight
          isOpen={rightSidebarOpen}
          onToggle={setRightSidebarOpen}
          equipmentId={selectedEquipmentId}
        />
      </div>

      {/* Sidebar bas */}
      <SidebarBottom
        isOpen={bottomSidebarOpen}
        onToggle={setBottomSidebarOpen}
      />

      {/* Overlays pour fermer les sidebars sur mobile */}
      <AnimatePresence>
        {(leftSidebarOpen || rightSidebarOpen) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-30 z-30 md:hidden"
            onClick={() => {
              setLeftSidebarOpen(false);
              setRightSidebarOpen(false);
            }}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Layout;