import { useMap } from 'react-leaflet';
import { useEffect, useRef, useState } from 'react';
import L from 'leaflet';

const VectorTileLayer = ({
  url = 'http://localhost:8001/tiles/{z}/{x}/{y}.pbf',
  options = {},
  visible = true
}) => {
  const map = useMap();
  const layerRef = useRef(null);
  const [vectorGridLoaded, setVectorGridLoaded] = useState(false);

  // Vérifier si leaflet.vectorgrid est disponible
  useEffect(() => {
    const checkVectorGrid = () => {
      if (window.L && window.L.VectorGrid) {
        console.log('✅ leaflet.vectorgrid disponible');
        setVectorGridLoaded(true);
      } else {
        setTimeout(checkVectorGrid, 100);
      }
    };
    checkVectorGrid();
  }, []);

  useEffect(() => {
    if (!map || !visible || !vectorGridLoaded) return;

    // Tes styles par défaut
    const defaultOptions = {
      vectorTileLayerStyles: {
        boundaries: {
          fill: false,
          stroke: true,
          color: '#1e40af',
          weight: 3,
          opacity: 0.8,
          dashArray: '5, 5'
        },
        cities: {
          fill: true,
          fillColor: '#3b82f6',
          fillOpacity: 0.8,
          stroke: true,
          color: '#ffffff',
          weight: 2,
          radius: 6
        }
      },
      interactive: true,
      getFeatureId: f => f.properties.id || f.properties.name,
      onEachFeature: (feature, layer) => {
        // ton code de popup/tooltip ici...
      },
    };

    // Ici on injecte noWrap, bounds, minNativeZoom & maxNativeZoom
    const finalOptions = {
      // Désactive le wrapping horizontal
      noWrap: true,
      // Limite la couche aux bornes exactes du Maroc
      bounds: L.latLngBounds([
        [20.5, -17.5],
        [36.0, -0.5]
      ]),
      // Aligne les zooms natifs
      minNativeZoom: 5,
      maxNativeZoom: 15,

      // Styles et handlers
      ...defaultOptions,

      // Tes overrides éventuels
      ...options,

      // Fusion fine des styles vectoriels
      vectorTileLayerStyles: {
        ...defaultOptions.vectorTileLayerStyles,
        ...(options.vectorTileLayerStyles || {})
      }
    };

    // Création et ajout de la couche
    const vectorGrid = L.vectorGrid.protobuf(url, finalOptions);
    vectorGrid.addTo(map);
    layerRef.current = vectorGrid;

    // Exemples d'événements
    vectorGrid.on('click', e => {
      if (e.layer?.getPopup()) e.layer.openPopup(e.latlng);
    });

    console.log('✅ Couche de tuiles vectorielles ajoutée:', url);

    return () => {
      if (layerRef.current) {
        map.removeLayer(layerRef.current);
        layerRef.current = null;
      }
    };
  }, [map, url, visible, vectorGridLoaded, options]);

  // Gère on/off
  useEffect(() => {
    if (layerRef.current) {
      if (visible && !map.hasLayer(layerRef.current)) {
        map.addLayer(layerRef.current);
      }
      if (!visible && map.hasLayer(layerRef.current)) {
        map.removeLayer(layerRef.current);
      }
    }
  }, [visible, map]);

  return null;
};

export default VectorTileLayer;
