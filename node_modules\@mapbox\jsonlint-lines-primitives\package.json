{"author": "<PERSON> <<EMAIL>> (http://zaa.ch)", "name": "@mapbox/jsonlint-lines-primitives", "description": "Validate JSON", "keywords": ["json", "validation", "lint", "jsonlint"], "version": "2.0.2", "preferGlobal": true, "repository": {"type": "git", "url": "git://github.com/mapbox/jsonlint.git"}, "bugs": {"url": "http://github.com/mapbox/jsonlint/issues"}, "main": "lib/jsonlint.js", "engines": {"node": ">= 0.6"}, "dependencies": {}, "devDependencies": {"test": "*", "jison": "*", "uglify-js": "*"}, "scripts": {"test": "node test/all-tests.js"}, "optionalDependencies": {}}