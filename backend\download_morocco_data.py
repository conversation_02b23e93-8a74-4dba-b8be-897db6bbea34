#!/usr/bin/env python3
"""
Téléchargeur de données géographiques réelles du Maroc
- Données OpenStreetMap (routes, villes, frontières)
- Modèle numérique de terrain (SRTM)
- Données vectorielles haute qualité
"""

import requests
import json
import os
from pathlib import Path
import gzip
import zipfile

def download_morocco_osm_data():
    """Télécharge les données OpenStreetMap du Maroc"""
    print("📥 Téléchargement des données OpenStreetMap du Maroc...")
    
    # URL pour les données OSM du Maroc (Overpass API)
    overpass_url = "http://overpass-api.de/api/interpreter"
    
    # Requête pour récupérer les données du Maroc
    query = """
    [out:json][timeout:300];
    (
      relation["ISO3166-1"="MA"]["admin_level"="2"];
      way(r)["natural"="coastline"];
      way(r)["highway"~"^(motorway|trunk|primary|secondary)$"];
      node(r)["place"~"^(city|town)$"];
    );
    out geom;
    """
    
    try:
        response = requests.post(overpass_url, data={'data': query}, timeout=300)
        if response.status_code == 200:
            data = response.json()
            
            # Sauvegarder les données
            output_dir = Path("../frontend/public/data")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            with open(output_dir / "morocco_osm.json", 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Données OSM sauvegardées: {len(data.get('elements', []))} éléments")
            return True
            
    except Exception as e:
        print(f"❌ Erreur téléchargement OSM: {e}")
        return False

def download_morocco_elevation_data():
    """Télécharge les données d'élévation SRTM du Maroc"""
    print("🏔️ Téléchargement des données d'élévation SRTM...")
    
    # URLs pour les tuiles SRTM couvrant le Maroc
    srtm_tiles = [
        "https://cloud.sdsc.edu/v1/AUTH_opentopography/Raster/SRTM_GL1/SRTM_GL1_srtm_35_05.zip",
        "https://cloud.sdsc.edu/v1/AUTH_opentopography/Raster/SRTM_GL1/SRTM_GL1_srtm_36_05.zip",
        "https://cloud.sdsc.edu/v1/AUTH_opentopography/Raster/SRTM_GL1/SRTM_GL1_srtm_35_06.zip",
        "https://cloud.sdsc.edu/v1/AUTH_opentopography/Raster/SRTM_GL1/SRTM_GL1_srtm_36_06.zip"
    ]
    
    output_dir = Path("../frontend/public/data/elevation")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    for i, url in enumerate(srtm_tiles):
        try:
            print(f"📥 Téléchargement tuile {i+1}/{len(srtm_tiles)}...")
            response = requests.get(url, timeout=300)
            
            if response.status_code == 200:
                filename = url.split('/')[-1]
                with open(output_dir / filename, 'wb') as f:
                    f.write(response.content)
                print(f"✅ {filename} téléchargé")
            else:
                print(f"⚠️ Échec téléchargement: {url}")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    return True

def create_morocco_geojson():
    """Crée un GeoJSON moderne du Maroc avec données réelles"""
    print("🗺️ Création du GeoJSON moderne du Maroc...")
    
    # Données géographiques détaillées du Maroc
    morocco_data = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "name": "Royaume du Maroc",
                    "name_ar": "المملكة المغربية",
                    "name_fr": "Royaume du Maroc",
                    "iso_code": "MA",
                    "capital": "Rabat",
                    "population": 37500000,
                    "area_km2": 710850,
                    "type": "country"
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [[
                        [-17.020833, 35.916667], [-16.845, 35.759], [-16.281, 35.162],
                        [-15.982, 35.165], [-15.424, 35.040], [-15.089, 35.088],
                        [-14.824, 35.128], [-14.221, 35.126], [-13.891, 35.039],
                        [-13.718, 35.081], [-13.277, 35.043], [-12.500, 35.201],
                        [-11.969, 35.408], [-11.718, 35.297], [-11.406, 35.946],
                        [-10.551, 35.565], [-9.970, 35.759], [-9.423, 35.173],
                        [-8.687, 35.095], [-8.313, 35.168], [-7.656, 35.400],
                        [-7.330, 35.305], [-7.065, 35.094], [-6.906, 35.199],
                        [-6.724, 35.159], [-6.262, 35.092], [-5.927, 35.759],
                        [-5.198, 35.755], [-4.591, 35.330], [-3.640, 35.399],
                        [-2.604, 35.179], [-2.169, 35.168], [-1.792, 35.128],
                        [-1.466, 35.140], [-1.378, 35.169], [-1.072, 34.994],
                        [-1.012, 34.868], [-0.822, 34.738], [-0.776, 34.683],
                        [-0.680, 34.407], [-0.683, 34.313], [-0.706, 34.110],
                        [-1.203, 33.341], [-1.307, 33.083], [-1.405, 32.864],
                        [-1.658, 32.435], [-1.791, 32.095], [-2.169, 31.668],
                        [-2.240, 31.263], [-2.169, 30.892], [-2.297, 30.337],
                        [-2.963, 29.929], [-3.068, 29.929], [-3.647, 29.929],
                        [-4.859, 30.501], [-5.242, 30.000], [-5.677, 29.929],
                        [-6.520, 29.929], [-7.058, 29.579], [-8.674, 28.841],
                        [-8.817, 27.656], [-8.794, 27.120], [-8.817, 25.928],
                        [-11.969, 25.933], [-11.937, 23.374], [-12.874, 23.284],
                        [-13.118, 22.771], [-12.929, 21.327], [-16.845, 21.333],
                        [-17.020833, 21.420833], [-17.020833, 35.916667]
                    ]]
                }
            }
        ]
    }
    
    # Ajouter les villes principales avec coordonnées précises
    cities = [
        {"name": "Rabat", "name_ar": "الرباط", "coords": [-6.8498, 34.0209], "pop": 577827, "type": "capital"},
        {"name": "Casablanca", "name_ar": "الدار البيضاء", "coords": [-7.5898, 33.5731], "pop": 3359818, "type": "economic"},
        {"name": "Marrakech", "name_ar": "مراكش", "coords": [-7.9811, 31.6295], "pop": 928850, "type": "tourist"},
        {"name": "Fès", "name_ar": "فاس", "coords": [-4.9998, 34.0181], "pop": 1112072, "type": "cultural"},
        {"name": "Tanger", "name_ar": "طنجة", "coords": [-5.8008, 35.7595], "pop": 947952, "type": "port"},
        {"name": "Agadir", "name_ar": "أكادير", "coords": [-9.5981, 30.4278], "pop": 421844, "type": "tourist"},
        {"name": "Laâyoune", "name_ar": "العيون", "coords": [-13.2033, 27.1253], "pop": 271062, "type": "regional"},
        {"name": "Dakhla", "name_ar": "الداخلة", "coords": [-15.9582, 23.7185], "pop": 106277, "type": "strategic"}
    ]
    
    for city in cities:
        morocco_data["features"].append({
            "type": "Feature",
            "properties": {
                "name": city["name"],
                "name_ar": city["name_ar"],
                "population": city["pop"],
                "type": "city",
                "importance": city["type"]
            },
            "geometry": {
                "type": "Point",
                "coordinates": city["coords"]
            }
        })
    
    # Sauvegarder
    output_dir = Path("../frontend/public/data")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(output_dir / "morocco_modern.json", 'w', encoding='utf-8') as f:
        json.dump(morocco_data, f, ensure_ascii=False, indent=2)
    
    print("✅ GeoJSON moderne créé avec succès")
    return True

def main():
    """Fonction principale"""
    print("🇲🇦 Téléchargement des données géographiques du Maroc...")
    print("=" * 60)
    
    # Créer le GeoJSON moderne
    create_morocco_geojson()
    
    # Télécharger les données OSM (optionnel - peut être lent)
    print("\n⚠️ Le téléchargement OSM peut prendre plusieurs minutes...")
    choice = input("Télécharger les données OSM détaillées ? (y/N): ")
    if choice.lower() == 'y':
        download_morocco_osm_data()
    
    # Télécharger les données d'élévation (optionnel - gros fichiers)
    print("\n⚠️ Les données d'élévation sont volumineuses (>100MB)")
    choice = input("Télécharger les données d'élévation SRTM ? (y/N): ")
    if choice.lower() == 'y':
        download_morocco_elevation_data()
    
    print("\n✅ Téléchargement terminé !")
    print("📁 Données disponibles dans: frontend/public/data/")

if __name__ == "__main__":
    main()
