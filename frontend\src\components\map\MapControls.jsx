import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Home,
  Layers,
  Maximize,
  Minimize,
  MapPin,
  Crosshair
} from 'lucide-react';
import { useMapStore } from '@/store/mapStore';
import { MAP_CONFIG } from '@/utils/constants';

const MapControls = ({ className, mapLayer, setMapLayer }) => {
  const [showLayerPanel, setShowLayerPanel] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const { 
    mapInstance, 
    mapZoom, 
    setMapCenter, 
    setMapZoom,
    activeTool,
    setActiveTool 
  } = useMapStore();

  const handleZoomIn = () => {
    if (mapInstance) {
      const newZoom = Math.min(mapZoom + 1, MAP_CONFIG.MAX_ZOOM);
      mapInstance.setZoom(newZoom);
      setMapZoom(newZoom);
    }
  };

  const handleZoomOut = () => {
    if (mapInstance) {
      const newZoom = Math.max(mapZoom - 1, MAP_CONFIG.MIN_ZOOM);
      mapInstance.setZoom(newZoom);
      setMapZoom(newZoom);
    }
  };

  const handleResetView = () => {
    if (mapInstance) {
      const center = [MAP_CONFIG.DEFAULT_LAT, MAP_CONFIG.DEFAULT_LNG];
      const zoom = MAP_CONFIG.DEFAULT_ZOOM;
      mapInstance.setView(center, zoom);
      setMapCenter(center);
      setMapZoom(zoom);
    }
  };

  const handleCenterOnUser = () => {
    if (navigator.geolocation && mapInstance) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const center = [latitude, longitude];
          mapInstance.setView(center, 15);
          setMapCenter(center);
          setMapZoom(15);
        },
        (error) => {
          console.error('Erreur de géolocalisation:', error);
        }
      );
    }
  };

  const handleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleCrosshair = () => {
    const newTool = activeTool === 'crosshair' ? null : 'crosshair';
    setActiveTool(newTool);
  };

  const controlButtons = [
    {
      id: 'zoom-in',
      icon: ZoomIn,
      label: 'Zoom avant',
      onClick: handleZoomIn,
      disabled: mapZoom >= MAP_CONFIG.MAX_ZOOM,
    },
    {
      id: 'zoom-out',
      icon: ZoomOut,
      label: 'Zoom arrière',
      onClick: handleZoomOut,
      disabled: mapZoom <= MAP_CONFIG.MIN_ZOOM,
    },
    {
      id: 'reset-view',
      icon: Home,
      label: 'Vue par défaut',
      onClick: handleResetView,
    },
    {
      id: 'center-user',
      icon: MapPin,
      label: 'Ma position',
      onClick: handleCenterOnUser,
    },
    {
      id: 'crosshair',
      icon: Crosshair,
      label: 'Réticule',
      onClick: handleCrosshair,
      active: activeTool === 'crosshair',
    },
    {
      id: 'layers',
      icon: Layers,
      label: 'Couches',
      onClick: () => setShowLayerPanel(!showLayerPanel),
      active: showLayerPanel,
    },
    {
      id: 'fullscreen',
      icon: isFullscreen ? Minimize : Maximize,
      label: isFullscreen ? 'Quitter plein écran' : 'Plein écran',
      onClick: handleFullscreen,
    },
  ];

  return (
    <div className={`flex flex-col space-y-2 ${className}`}>
      {/* Contrôles principaux */}
      <div className="bg-c2-gray-300 border border-c2-gray-200 rounded-lg shadow-lg overflow-hidden">
        {controlButtons.map((button, index) => {
          const Icon = button.icon;
          const isActive = button.active;
          const isDisabled = button.disabled;
          
          return (
            <button
              key={button.id}
              onClick={button.onClick}
              disabled={isDisabled}
              title={button.label}
              className={`w-10 h-10 flex items-center justify-center transition-colors ${
                index > 0 ? 'border-t border-c2-gray-200' : ''
              } ${
                isActive 
                  ? 'bg-c2-blue text-white' 
                  : isDisabled
                  ? 'text-c2-gray-100 cursor-not-allowed'
                  : 'text-c2-white hover:bg-c2-gray-200'
              }`}
            >
              <Icon className="w-5 h-5" />
            </button>
          );
        })}
      </div>

      {/* Panneau des couches */}
      {showLayerPanel && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 20 }}
          className="bg-c2-gray-300 border border-c2-gray-200 rounded-lg shadow-lg p-3 min-w-[200px]"
        >
          <h3 className="text-sm font-semibold text-c2-white mb-3">Couches de carte</h3>
          
          <div className="space-y-2">
            {/* Couche de base */}
            <div>
              <label className="flex items-center space-x-2 text-sm text-c2-white cursor-pointer">
                <input
                  type="radio"
                  name="base-layer"
                  checked={mapLayer === 'street'}
                  onChange={() => setMapLayer('street')}
                  className="text-c2-blue"
                />
                <span>🗺️ Plan</span>
              </label>
            </div>

            <div>
              <label className="flex items-center space-x-2 text-sm text-c2-white cursor-pointer">
                <input
                  type="radio"
                  name="base-layer"
                  checked={mapLayer === 'satellite'}
                  onChange={() => setMapLayer('satellite')}
                  className="text-c2-blue"
                />
                <span>🛰️ Satellite</span>
              </label>
            </div>

            <div>
              <label className="flex items-center space-x-2 text-sm text-c2-white cursor-pointer">
                <input
                  type="radio"
                  name="base-layer"
                  checked={mapLayer === 'hybrid'}
                  onChange={() => setMapLayer('hybrid')}
                  className="text-c2-blue"
                />
                <span>🌍 Hybride</span>
              </label>
            </div>

            <hr className="border-c2-gray-200 my-2" />

            {/* Couches overlay */}
            <div>
              <label className="flex items-center space-x-2 text-sm text-c2-white">
                <input
                  type="checkbox"
                  defaultChecked
                  className="text-c2-blue"
                />
                <span>Équipements</span>
              </label>
            </div>
            
            <div>
              <label className="flex items-center space-x-2 text-sm text-c2-white">
                <input
                  type="checkbox"
                  className="text-c2-blue"
                />
                <span>Zones de couverture</span>
              </label>
            </div>
            
            <div>
              <label className="flex items-center space-x-2 text-sm text-c2-white">
                <input
                  type="checkbox"
                  className="text-c2-blue"
                />
                <span>Secteurs</span>
              </label>
            </div>
            
            <div>
              <label className="flex items-center space-x-2 text-sm text-c2-white">
                <input
                  type="checkbox"
                  className="text-c2-blue"
                />
                <span>Trajectoires</span>
              </label>
            </div>


          </div>
        </motion.div>
      )}

      {/* Indicateur de zoom */}
      <div className="bg-c2-gray-300 border border-c2-gray-200 rounded px-2 py-1 text-xs text-c2-white text-center font-mono">
        Zoom: {mapZoom}
      </div>
    </div>
  );
};

export default MapControls;
