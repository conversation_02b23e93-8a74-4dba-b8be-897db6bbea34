import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';                  // Leaflet
import 'leaflet.vectorgrid'; 

import App from './App';
import './styles/globals.css';

// Configuration React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: (failureCount, error) => {
        // Ne pas retry sur les erreurs d'authentification
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          return false;
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

// Configuration des toasts
const toastOptions = {
  duration: 4000,
  position: 'top-right',
  style: {
    background: '#333333',
    color: '#ffffff',
    border: '1px solid #555555',
  },
  success: {
    iconTheme: {
      primary: '#4CAF50',
      secondary: '#ffffff',
    },
  },
  error: {
    iconTheme: {
      primary: '#F44336',
      secondary: '#ffffff',
    },
  },
};

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true,
      }}
    >
      <QueryClientProvider client={queryClient}>
        <App />
        <Toaster toastOptions={toastOptions} />
        {import.meta.env.DEV && <ReactQueryDevtools initialIsOpen={false} />}
      </QueryClientProvider>
    </BrowserRouter>
  </React.StrictMode>
);
