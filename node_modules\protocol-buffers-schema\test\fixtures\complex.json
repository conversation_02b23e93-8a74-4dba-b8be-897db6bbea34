{"syntax": 3, "package": "tutorial", "imports": [], "enums": [], "messages": [{"name": "Person", "options": {}, "enums": [{"name": "PhoneType", "values": {"MOBILE": {"value": 0, "options": {"some_enum_option": "true"}}, "HOME": {"value": 1, "options": {}}, "WORK": {"value": 2, "options": {}}}, "options": {"allow_alias": true, "custom_option": true}}], "extends": [], "messages": [{"name": "PhoneNumber", "enums": [], "extends": [], "messages": [], "options": {}, "fields": [{"name": "number", "type": "string", "tag": 1, "map": null, "oneof": null, "required": true, "repeated": false, "options": {}}, {"name": "type", "type": "PhoneType", "tag": 2, "map": null, "oneof": null, "required": false, "repeated": false, "options": {"default": "HOME"}}], "extensions": null}], "fields": [{"name": "name", "type": "string", "tag": 1, "map": null, "oneof": null, "required": true, "repeated": false, "options": {}}, {"name": "id", "type": "int32", "tag": 2, "map": null, "oneof": null, "required": true, "repeated": false, "options": {}}, {"name": "email", "type": "string", "tag": 3, "map": null, "oneof": null, "required": false, "repeated": false, "options": {}}, {"name": "phone", "type": "PhoneNumber", "tag": 4, "map": null, "oneof": null, "required": false, "repeated": true, "options": {}}], "extensions": null}, {"name": "AddressBook", "enums": [], "extends": [], "messages": [], "options": {}, "fields": [{"name": "person", "type": "Person", "tag": 1, "map": null, "oneof": null, "required": false, "repeated": true, "options": {}}], "extensions": null}], "options": {"java_package": "com.mafintosh.generated", "java_outer_classname": "Example", "java_generate_equals_and_hash": true, "optimize_for": "SPEED"}, "extends": []}