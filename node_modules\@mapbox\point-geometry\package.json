{"name": "@mapbox/point-geometry", "version": "0.1.0", "description": "a point geometry with transforms", "main": "index.js", "scripts": {"test": "tape test.js", "doc": "dox -r < index.js | doxme --readme > README.md", "cov": "istanbul cover test.js && coveralls < ./coverage/lcov.info"}, "repository": {"type": "git", "url": "**************:mapbox/point-geometry.git"}, "keywords": ["point", "geometry", "primitive"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/mapbox/point-geometry/issues"}, "homepage": "https://github.com/mapbox/point-geometry", "devDependencies": {"coveralls": "~2.10.1", "dox": "^0.6.1", "doxme": "^1.8.2", "istanbul": "~0.2.11", "tape": "~2.13.3"}}