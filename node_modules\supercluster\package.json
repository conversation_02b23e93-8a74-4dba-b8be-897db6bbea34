{"name": "supercluster", "version": "8.0.1", "description": "A very fast geospatial point clustering library.", "main": "dist/supercluster.js", "type": "module", "exports": "./index.js", "module": "index.js", "jsdelivr": "dist/supercluster.min.js", "unpkg": "dist/supercluster.min.js", "sideEffects": false, "scripts": {"pretest": "eslint index.js bench.js test/test.js demo/index.js demo/worker.js", "test": "node test/test.js", "cov": "c8 npm run test", "bench": "node --expose-gc bench.js", "build": "mkdirp dist && rollup -c", "prepublishOnly": "npm run test && npm run build"}, "files": ["index.js", "dist/supercluster.js", "dist/supercluster.min.js"], "repository": {"type": "git", "url": "git://github.com/mapbox/supercluster.git"}, "keywords": ["clustering", "geospatial", "markers"], "author": "<PERSON>", "license": "ISC", "dependencies": {"kdbush": "^4.0.2"}, "devDependencies": {"@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-terser": "^0.4.1", "c8": "^7.13.0", "eslint": "^8.39.0", "eslint-config-mourner": "^3.0.0", "mkdirp": "^3.0.1", "rollup": "^3.21.0"}, "eslintConfig": {"extends": "mourner", "parserOptions": {"ecmaVersion": 2020}, "rules": {"camelcase": 0}}}