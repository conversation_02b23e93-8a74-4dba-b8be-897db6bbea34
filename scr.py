import os

tiles_root = r"C:\Users\<USER>\Desktop\C2-EW\tiles"

tile_count = 0
zoom_levels = set()

for root, dirs, files in os.walk(tiles_root):
    for file in files:
        if file.endswith(".pbf"):
            tile_count += 1
            parts = os.path.relpath(root, tiles_root).split(os.sep)
            if len(parts) >= 1:
                zoom_levels.add(parts[0])

print(f"✅ Total de tuiles : {tile_count}")
print(f"🔢 Niveaux de zoom disponibles : {sorted(zoom_levels)}")
