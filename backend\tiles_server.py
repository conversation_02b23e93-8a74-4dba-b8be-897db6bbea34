from fastapi import FastAP<PERSON>, HTTPException, Response, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pathlib import Path
import asyncio
import logging
from contextlib import asynccontextmanager

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Dossier des tuiles (relatif au dossier courant)
TILES_DIR = Path("tiles").resolve()

class TileStats:
    """Gestionnaire des statistiques des tuiles"""
    def __init__(self, tiles_dir: Path):
        self.tiles_dir = tiles_dir
        self._stats = None

    def scan_tiles(self) -> dict:
        if not self.tiles_dir.exists():
            return {
                "available": False,
                "total_tiles": 0,
                "zoom_levels": [],
                "error": f"Répertoire non trouvé: {self.tiles_dir}"
            }
        zoom_levels = set()
        total_tiles = 0
        try:
            for zoom_dir in self.tiles_dir.iterdir():
                if zoom_dir.is_dir() and zoom_dir.name.isdigit():
                    z = int(zoom_dir.name)
                    zoom_levels.add(z)
                    for x_dir in zoom_dir.iterdir():
                        if x_dir.is_dir() and x_dir.name.isdigit():
                            total_tiles += sum(1 for f in x_dir.iterdir() if f.suffix == ".json")
            stats = {
                "available": True,
                "total_tiles": total_tiles,
                "zoom_levels": sorted(zoom_levels),
                "tiles_directory": str(self.tiles_dir),
                "min_zoom": min(zoom_levels) if zoom_levels else 0,
                "max_zoom": max(zoom_levels) if zoom_levels else 0
            }
        except Exception as e:
            logger.error(f"Erreur scan_tiles: {e}")
            stats = {
                "available": False,
                "total_tiles": 0,
                "zoom_levels": [],
                "error": str(e)
            }
        self._stats = stats
        return stats

    def get_stats(self) -> dict:
        if self._stats is None:
            return self.scan_tiles()
        return self._stats

tile_stats = TileStats(TILES_DIR)

@asynccontextmanager
async def lifespan(app: FastAPI):
    stats = tile_stats.scan_tiles()
    if stats["available"]:
        logger.info(f"✅ {stats['total_tiles']} tuiles trouvées aux niveaux {stats['zoom_levels']}")
    else:
        logger.error(f"❌ Erreur au scan des tuiles: {stats.get('error')}")
    logger.info("🚀 Serveur prêt sur http://localhost:8001")
    yield
    # Ici tu peux ajouter du code de nettoyage si besoin

app = FastAPI(
    title="C2-EW Serveur de Tuiles Vectorielles",
    description="Serveur de tuiles vectorielles pour carte hors ligne du Maroc",
    version="1.0.0",
    lifespan=lifespan
)

# CORS (pour tests hors ligne)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    stats = tile_stats.get_stats()
    return {
        "service": "C2-EW Serveur de Tuiles Vectorielles",
        "version": "1.0.0",
        "status": "online",
        "tiles": stats,
        "endpoints": {
            "tiles": "/tiles/{z}/{x}/{y}.pbf",
            "metadata": "/tiles/metadata",
            "health": "/health",
            "stats": "/stats"
        }
    }

@app.get("/health")
async def health_check():
    stats = tile_stats.get_stats()
    return {
        "status": "healthy" if stats["available"] else "unhealthy",
        "tiles_available": stats["available"],
        "total_tiles": stats["total_tiles"],
        "timestamp": asyncio.get_event_loop().time()
    }

@app.get("/stats")
async def get_stats():
    return tile_stats.get_stats()

@app.get("/tiles/metadata")
async def get_metadata():
    stats = tile_stats.get_stats()
    if not stats["available"]:
        raise HTTPException(status_code=503, detail="Tuiles non disponibles")
    return {
        "tilejson": "2.2.0",
        "name": "Maroc - Tuiles Vectorielles C2-EW",
        "description": "Tuiles vectorielles hors ligne du Royaume du Maroc",
        "version": "1.0.0",
        "attribution": "© C2-EW System",
        "scheme": "xyz",
        "tiles": ["http://localhost:8001/tiles/{z}/{x}/{y}.pbf"],
        "minzoom": stats["min_zoom"],
        "maxzoom": stats["max_zoom"],
        "bounds": [-17.5, 20.5, -0.5, 36.0],
        "center": [-8.0, 29.0, 6],
        "format": "pbf",
        "type": "vector",
        "vector_layers": [
            {
                "id": "admin_0",
                "description": "Frontières nationales",
                "minzoom": 0,
                "maxzoom": 15,
                "fields": {
                    "name": "Nom",
                    "name_ar": "Nom en arabe",
                    "admin_level": "Niveau administratif"
                }
            },
            {
                "id": "admin_1",
                "description": "Régions",
                "minzoom": 5,
                "maxzoom": 15,
                "fields": {
                    "name": "Nom de la région",
                    "name_ar": "Nom en arabe",
                    "admin_level": "Niveau administratif"
                }
            },
            {
                "id": "admin_2",
                "description": "Provinces/Préfectures",
                "minzoom": 7,
                "maxzoom": 15,
                "fields": {
                    "name": "Nom",
                    "name_ar": "Nom en arabe",
                    "admin_level": "Niveau administratif"
                }
            },
            {
                "id": "populated_places",
                "description": "Villes et localités",
                "minzoom": 5,
                "maxzoom": 15,
                "fields": {
                    "name": "Nom",
                    "name_ar": "Nom en arabe",
                    "population": "Population",
                    "capital": "Statut de capitale",
                    "type": "Type de localité"
                }
            }
        ]
    }

@app.get("/tiles/{z}/{x}/{y}.json")
async def get_tile(z: int, x: int, y: int):
    """Servir une tuile vectorielle JSON (vide si absente)"""

    if z < 0 or z > 20 or x < 0 or y < 0:
        raise HTTPException(status_code=400, detail="Paramètres de tuile invalides")

    tile_path = TILES_DIR / str(z) / str(x) / f"{y}.json"
    rel = tile_path.relative_to(TILES_DIR.parent)
    logger.info(f"🔍 Recherche tuile: {rel}")

    if tile_path.exists():
        logger.info(f"✅ Tuile trouvée: {rel}")
        return FileResponse(
            path=tile_path,
            media_type="application/json",
            headers={
                "Content-Encoding": "gzip",
                "Cache-Control": "public, max-age=3600",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET",
                "Access-Control-Allow-Headers": "*",
            }
        )

    logger.warning(f"⚠️ Tuile manquante z={z}, x={x}, y={y} → PBF vide")
    empty_pbf = (
        b'\x1f\x8b\x08\x00\x00\x00\x00\x00\x00\xff'
        b'\x03\x00\x00\x00\x00\x00\x00\x00\x00\x00'
    )
    return Response(
        content=empty_pbf,
        media_type="application/x-protobuf",
        headers={
            "Content-Encoding": "gzip",
            "Cache-Control": "public, max-age=3600",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET",
            "Access-Control-Allow-Headers": "*",
        }
    )

@app.get("/tiles/{z}/{x}/{y}.json")
async def get_tile_info(z: int, x: int, y: int):
    tile_path = TILES_DIR / str(z) / str(x) / f"{y}.pbf"
    if not tile_path.exists():
        raise HTTPException(status_code=404, detail="Tuile non trouvée")
    stat = tile_path.stat()
    return {
        "z": z,
        "x": x,
        "y": y,
        "exists": True,
        "size": stat.st_size,
        "modified": stat.st_mtime,
        "path": str(tile_path.relative_to(TILES_DIR.parent))
    }

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start = asyncio.get_event_loop().time()
    response = await call_next(request)
    duration = asyncio.get_event_loop().time() - start
    if response.status_code >= 400:
        logger.warning(f"❌ {request.method} {request.url} - {response.status_code} ({duration:.3f}s)")
    return response

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("tiles_server:app", host="0.0.0.0", port=8001, reload=True, log_level="info")
