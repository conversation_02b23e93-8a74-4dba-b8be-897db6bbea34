import React, { useRef, useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { motion } from 'framer-motion';

// Configuration Mapbox offline (pas de token nécessaire)
mapboxgl.accessToken = '';

const ModernMoroccoMap = ({ onEquipmentClick, className = "" }) => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapStyle, setMapStyle] = useState('satellite'); // satellite, streets, dark, light
  const [showTerrain, setShowTerrain] = useState(true);
  const [show3D, setShow3D] = useState(false);

  // Styles de carte offline disponibles
  const mapStyles = {
    satellite: 'http://localhost:8003/style/satellite.json',
    streets: 'http://localhost:8003/style/streets.json',
    dark: 'http://localhost:8003/style/dark.json',
    light: 'http://localhost:8003/style/light.json',
    terrain: 'http://localhost:8003/style/satellite.json' // Utilise satellite pour terrain
  };

  useEffect(() => {
    if (map.current) return; // Éviter la réinitialisation

    // Initialiser la carte
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: mapStyles[mapStyle],
      center: [-7.0926, 31.7917], // Centre du Maroc
      zoom: 5.5,
      pitch: show3D ? 45 : 0,
      bearing: 0,
      antialias: true,
      maxBounds: [[-18, 20], [-1, 37]] // Limites du Maroc
    });

    // Contrôles de navigation
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');
    map.current.addControl(new mapboxgl.ScaleControl(), 'bottom-left');
    map.current.addControl(new mapboxgl.FullscreenControl(), 'top-right');

    // Événement de chargement
    map.current.on('load', () => {
      setMapLoaded(true);
      console.log('🗺️ Carte moderne du Maroc chargée');
      
      // Ajouter le terrain 3D
      if (showTerrain) {
        addTerrainLayer();
      }
      
      // Charger les données du Maroc
      loadMoroccoData();
    });

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  // Ajouter la couche de terrain 3D
  const addTerrainLayer = () => {
    if (!map.current || !mapLoaded) return;

    try {
      // Source de terrain Mapbox
      map.current.addSource('mapbox-dem', {
        type: 'raster-dem',
        url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
        tileSize: 512,
        maxzoom: 14
      });

      // Activer le terrain 3D
      map.current.setTerrain({ source: 'mapbox-dem', exaggeration: 1.5 });

      // Ajouter une couche de relief
      map.current.addLayer({
        id: 'hillshading',
        type: 'hillshade',
        source: 'mapbox-dem',
        layout: {},
        paint: {
          'hillshade-shadow-color': '#473B24',
          'hillshade-highlight-color': '#FFFFFF',
          'hillshade-exaggeration': 0.8
        }
      });

      console.log('🏔️ Terrain 3D activé');
    } catch (error) {
      console.warn('⚠️ Terrain 3D non disponible:', error);
    }
  };

  // Charger les données géographiques du Maroc
  const loadMoroccoData = async () => {
    if (!map.current || !mapLoaded) return;

    try {
      // Charger le GeoJSON du Maroc
      const response = await fetch('/data/morocco_modern.json');
      const moroccoData = await response.json();

      // Ajouter la source des données
      map.current.addSource('morocco', {
        type: 'geojson',
        data: moroccoData
      });

      // Couche des frontières du Maroc
      map.current.addLayer({
        id: 'morocco-borders',
        type: 'line',
        source: 'morocco',
        filter: ['==', ['get', 'type'], 'country'],
        paint: {
          'line-color': '#FF6B35',
          'line-width': 3,
          'line-opacity': 0.8
        }
      });

      // Couche de remplissage du territoire
      map.current.addLayer({
        id: 'morocco-fill',
        type: 'fill',
        source: 'morocco',
        filter: ['==', ['get', 'type'], 'country'],
        paint: {
          'fill-color': '#FF6B35',
          'fill-opacity': 0.1
        }
      });

      // Couche des villes
      map.current.addLayer({
        id: 'morocco-cities',
        type: 'circle',
        source: 'morocco',
        filter: ['==', ['get', 'type'], 'city'],
        paint: {
          'circle-radius': [
            'interpolate',
            ['linear'],
            ['get', 'population'],
            100000, 4,
            500000, 6,
            1000000, 8,
            3000000, 12
          ],
          'circle-color': '#FF6B35',
          'circle-stroke-color': '#FFFFFF',
          'circle-stroke-width': 2,
          'circle-opacity': 0.8
        }
      });

      // Labels des villes
      map.current.addLayer({
        id: 'morocco-city-labels',
        type: 'symbol',
        source: 'morocco',
        filter: ['==', ['get', 'type'], 'city'],
        layout: {
          'text-field': ['get', 'name'],
          'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
          'text-size': 12,
          'text-offset': [0, -2],
          'text-anchor': 'bottom'
        },
        paint: {
          'text-color': '#2C3E50',
          'text-halo-color': '#FFFFFF',
          'text-halo-width': 2
        }
      });

      console.log('🇲🇦 Données du Maroc chargées');
    } catch (error) {
      console.error('❌ Erreur chargement données:', error);
    }
  };

  // Changer le style de carte
  const changeMapStyle = (newStyle) => {
    if (!map.current || !mapLoaded) return;
    
    setMapStyle(newStyle);
    map.current.setStyle(mapStyles[newStyle]);
    
    // Recharger les données après changement de style
    map.current.once('styledata', () => {
      if (showTerrain) {
        addTerrainLayer();
      }
      loadMoroccoData();
    });
  };

  // Basculer la vue 3D
  const toggle3D = () => {
    if (!map.current || !mapLoaded) return;
    
    const newPitch = show3D ? 0 : 45;
    setShow3D(!show3D);
    
    map.current.easeTo({
      pitch: newPitch,
      duration: 1000
    });
  };

  // Calculer la visibilité optique (ligne de vue)
  const calculateLineOfSight = (point1, point2) => {
    // Implémentation simplifiée - nécessiterait des données d'élévation réelles
    console.log('🔍 Calcul de visibilité optique:', point1, point2);
    // TODO: Implémenter avec les données SRTM
  };

  return (
    <motion.div 
      className={`relative w-full h-full ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Conteneur de la carte */}
      <div ref={mapContainer} className="w-full h-full" />
      
      {/* Contrôles personnalisés */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <div className="space-y-2">
          <h3 className="text-sm font-semibold text-gray-800">Styles de carte</h3>
          
          {/* Sélecteur de style */}
          <div className="grid grid-cols-2 gap-1">
            {Object.keys(mapStyles).map((style) => (
              <button
                key={style}
                onClick={() => changeMapStyle(style)}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  mapStyle === style 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {style === 'satellite' ? '🛰️ Satellite' :
                 style === 'streets' ? '🗺️ Routes' :
                 style === 'dark' ? '🌙 Nuit' :
                 style === 'light' ? '☀️ Jour' :
                 '🏔️ Terrain'}
              </button>
            ))}
          </div>
          
          {/* Contrôles 3D */}
          <div className="pt-2 border-t border-gray-200">
            <button
              onClick={toggle3D}
              className={`w-full px-2 py-1 text-xs rounded transition-colors ${
                show3D 
                  ? 'bg-green-500 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {show3D ? '📐 Vue 2D' : '🏔️ Vue 3D'}
            </button>
          </div>
        </div>
      </div>
      
      {/* Indicateur de chargement */}
      {!mapLoaded && (
        <div className="absolute inset-0 bg-gray-900/50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-4 shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
              <span className="text-gray-700">Chargement de la carte moderne...</span>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default ModernMoroccoMap;
